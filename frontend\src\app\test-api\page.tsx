"use client";

import React, { useState } from 'react';
import axios from 'axios';

export default function TestApiPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testDirectAxios = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("Making direct axios request...");
      const response = await axios.get("/api/products?featured=true", {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });
      console.log("Axios response:", response);
      console.log("Axios response data:", response.data);
      setResult(response.data);
    } catch (err: any) {
      console.error("Axios test error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const testSanctumCSRF = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("Testing Sanctum CSRF...");
      const response = await axios.get("/sanctum/csrf-cookie", {
        withCredentials: true,
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });
      console.log("Sanctum CSRF response:", response);
      setResult({ message: "CSRF token fetched successfully", status: response.status });
    } catch (err: any) {
      console.error("Sanctum CSRF test error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  const testCategories = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log("Testing categories...");
      const response = await axios.get("/api/categories", {
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });
      console.log("Categories response:", response);
      console.log("Categories response data:", response.data);
      setResult(response.data);
    } catch (err: any) {
      console.error("Categories test error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">API Test Page</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testDirectAxios}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-4"
        >
          {loading ? "Loading..." : "Test Products API"}
        </button>
        
        <button
          onClick={testSanctumCSRF}
          disabled={loading}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-4"
        >
          {loading ? "Loading..." : "Test Sanctum CSRF"}
        </button>
        
        <button
          onClick={testCategories}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
        >
          {loading ? "Loading..." : "Test Categories API"}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-bold mb-2">Result:</h2>
          <pre className="whitespace-pre-wrap overflow-auto max-h-96">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
