{"version": 3, "sources": [], "sections": [{"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { AuthService, CartService } from '../services';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [cartItemCount, setCartItemCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check authentication status\n    setIsAuthenticated(AuthService.isAuthenticated());\n    \n    // Get cart item count\n    setCartItemCount(CartService.getItemCount());\n    \n    // Add event listener for cart updates\n    const handleStorageChange = () => {\n      setCartItemCount(CartService.getItemCount());\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    await AuthService.logout();\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <Link to=\"/\" className=\"text-2xl font-bold text-indigo-600\">\n                  EcommerceApp\n                </Link>\n              </div>\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                <Link\n                  to=\"/\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Home\n                </Link>\n                <Link\n                  to=\"/products\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Products\n                </Link>\n                <Link\n                  to=\"/categories\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Categories\n                </Link>\n              </nav>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\n              <Link to=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {cartItemCount > 0 && (\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\n                    {cartItemCount}\n                  </span>\n                )}\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"p-1 rounded-full text-gray-400 hover:text-gray-500\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n            <div className=\"-mr-2 flex items-center sm:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1\">\n              <Link\n                to=\"/\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                to=\"/products\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Products\n              </Link>\n              <Link\n                to=\"/categories\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link\n                to=\"/cart\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Cart ({cartItemCount})\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main content */}\n      <main className=\"flex-grow\">\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-gray-500 text-sm\">\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AALA;;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,mBAAmB,4KAAA,CAAA,cAAW,CAAC,eAAe;QAE9C,sBAAsB;QACtB,iBAAiB,4KAAA,CAAA,cAAW,CAAC,YAAY;QAEzC,sCAAsC;QACtC,MAAM,sBAAsB;YAC1B,iBAAiB,4KAAA,CAAA,cAAW,CAAC,YAAY;QAC3C;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,4KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uJAAA,CAAA,OAAI;gDAAC,IAAG;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,uJAAA,CAAA,OAAI;4CAAC,IAAG;4CAAQ,WAAU;;8DACzB,8OAAC,+NAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DAEV,cAAA,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;8DAE5C,8OAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,8OAAC,uJAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,8OAAC,iNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,8OAAC,uJAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,8OAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,8OAAC,uJAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;uCAEe", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Product, CartService } from '../services';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const handleAddToCart = () => {\n    CartService.addToCart(product, 1);\n    // Trigger storage event to update cart count in Layout\n    window.dispatchEvent(new Event('storage'));\n  };\n\n  return (\n    <div className=\"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden\">\n      <div className=\"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"flex flex-col space-y-2 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">\n          <Link href={`/products/${product.slug}`} className=\"block\">\n            <span aria-hidden=\"true\" className=\"absolute inset-0\" />\n            {product.name}\n          </Link>\n        </h3>\n        <p className=\"text-sm text-gray-500 line-clamp-2\">{product.description}</p>\n        <div className=\"flex justify-between items-center\">\n          <p className=\"text-lg font-medium text-gray-900\">${Number(product.price).toFixed(2)}</p>\n          {product.quantity > 0 ? (\n            <span className=\"text-sm text-green-600\">In Stock</span>\n          ) : (\n            <span className=\"text-sm text-red-600\">Out of Stock</span>\n          )}\n        </div>\n        <button\n          onClick={handleAddToCart}\n          disabled={product.quantity <= 0}\n          className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${\n            product.quantity > 0\n              ? 'bg-indigo-600 hover:bg-indigo-700'\n              : 'bg-gray-400 cursor-not-allowed'\n          }`}\n        >\n          Add to Cart\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,MAAM,kBAAkB;QACtB,4KAAA,CAAA,cAAW,CAAC,SAAS,CAAC,SAAS;QAC/B,uDAAuD;QACvD,OAAO,aAAa,CAAC,IAAI,MAAM;IACjC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,KAAK,iBACZ,8OAAC;oBACC,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,IAAI;oBACjB,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;4BAAE,WAAU;;8CACjD,8OAAC;oCAAK,eAAY;oCAAO,WAAU;;;;;;gCAClC,QAAQ,IAAI;;;;;;;;;;;;kCAGjB,8OAAC;wBAAE,WAAU;kCAAsC,QAAQ,WAAW;;;;;;kCACtE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;oCAAoC;oCAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;4BAChF,QAAQ,QAAQ,GAAG,kBAClB,8OAAC;gCAAK,WAAU;0CAAyB;;;;;qDAEzC,8OAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,8OAAC;wBACC,SAAS;wBACT,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,WAAW,CAAC,kIAAkI,EAC5I,QAAQ,QAAQ,GAAG,IACf,sCACA,kCACJ;kCACH;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Category } from '../services';\n\ninterface CategoryCardProps {\n  category: Category;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link\n      href={`/categories/${category.slug}`}\n      className=\"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\"\n    >\n      <div className=\"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40\">\n        {category.image ? (\n          <img\n            src={category.image}\n            alt={category.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{category.name}</h3>\n        {category.description && (\n          <p className=\"mt-1 text-sm text-gray-500 line-clamp-2\">{category.description}</p>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QACpC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,iBACb,8OAAC;oBACC,KAAK,SAAS,KAAK;oBACnB,KAAK,SAAS,IAAI;oBAClB,WAAU;;;;;yCAGZ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqC,SAAS,IAAI;;;;;;oBAC/D,SAAS,WAAW,kBACnB,8OAAC;wBAAE,WAAU;kCAA2C,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAKtF;uCAEe", "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/StripePaymentForm.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useStripe, useElements, CardElement } from \"@stripe/react-stripe-js\";\nimport { StripeCardElementOptions } from \"@stripe/stripe-js\";\n\ninterface StripePaymentFormProps {\n  onSuccess: (paymentMethodId: string) => void;\n  onError: (error: string) => void;\n  loading: boolean;\n  setLoading: (loading: boolean) => void;\n}\n\nconst cardElementOptions: StripeCardElementOptions = {\n  style: {\n    base: {\n      fontSize: \"16px\",\n      color: \"#424770\",\n      \"::placeholder\": {\n        color: \"#aab7c4\",\n      },\n    },\n    invalid: {\n      color: \"#9e2146\",\n    },\n  },\n};\n\nconst StripePaymentForm: React.FC<StripePaymentFormProps> = ({\n  onSuccess,\n  onError,\n  loading,\n  setLoading,\n}) => {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [cardError, setCardError] = useState<string | null>(null);\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    const cardElement = elements.getElement(CardElement);\n\n    if (!cardElement) {\n      return;\n    }\n\n    setLoading(true);\n    setCardError(null);\n\n    try {\n      // Create payment method\n      const { error, paymentMethod } = await stripe.createPaymentMethod({\n        type: \"card\",\n        card: cardElement,\n      });\n\n      if (error) {\n        setCardError(error.message || \"An error occurred during payment\");\n        onError(error.message || \"An error occurred during payment\");\n      } else if (paymentMethod) {\n        onSuccess(paymentMethod.id);\n      }\n    } catch {\n      setCardError(\"Payment failed. Please try again.\");\n      onError(\"Payment failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCardChange = (event: { error?: { message: string } }) => {\n    if (event.error) {\n      setCardError(event.error.message);\n    } else {\n      setCardError(null);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Card Information\n        </label>\n        <div className=\"border border-gray-300 rounded-md p-3 bg-white\">\n          <CardElement\n            options={cardElementOptions}\n            onChange={handleCardChange}\n          />\n        </div>\n        {cardError && <p className=\"mt-1 text-sm text-red-600\">{cardError}</p>}\n      </div>\n\n      <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-blue-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-blue-700\">\n              This is a test environment. Use test card number 4242 4242 4242\n              4242 with any future expiry date and any 3-digit CVC.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className={`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${\n          !stripe || loading\n            ? \"bg-gray-400 cursor-not-allowed\"\n            : \"bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\n        }`}\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2\"></div>\n            Processing Payment...\n          </>\n        ) : (\n          \"Pay Now\"\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default StripePaymentForm;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAUA,MAAM,qBAA+C;IACnD,OAAO;QACL,MAAM;YACJ,UAAU;YACV,OAAO;YACP,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,SAAS;YACP,OAAO;QACT;IACF;AACF;AAEA,MAAM,oBAAsD,CAAC,EAC3D,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACX;IACC,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,cAAc,SAAS,UAAU,CAAC,oLAAA,CAAA,cAAW;QAEnD,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,OAAO,mBAAmB,CAAC;gBAChE,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,OAAO;gBACT,aAAa,MAAM,OAAO,IAAI;gBAC9B,QAAQ,MAAM,OAAO,IAAI;YAC3B,OAAO,IAAI,eAAe;gBACxB,UAAU,cAAc,EAAE;YAC5B;QACF,EAAE,OAAM;YACN,aAAa;YACb,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,KAAK,EAAE;YACf,aAAa,MAAM,KAAK,CAAC,OAAO;QAClC,OAAO;YACL,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oLAAA,CAAA,cAAW;4BACV,SAAS;4BACT,UAAU;;;;;;;;;;;oBAGb,2BAAa,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,8OAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;sCAIf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,8OAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAW,CAAC,+HAA+H,EACzI,CAAC,UAAU,UACP,mCACA,oDACJ;0BAED,wBACC;;sCACE,8OAAC;4BAAI,WAAU;;;;;;wBAAkF;;mCAInG;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  color?: 'primary' | 'secondary' | 'white';\n  text?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ \n  size = 'md', \n  color = 'primary', \n  text,\n  className = ''\n}) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n    xl: 'h-16 w-16'\n  };\n\n  const colorClasses = {\n    primary: 'border-indigo-500',\n    secondary: 'border-gray-500',\n    white: 'border-white'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div\n        className={`animate-spin rounded-full border-t-2 border-b-2 ${sizeClasses[size]} ${colorClasses[color]}`}\n      />\n      {text && (\n        <p className={`mt-2 text-gray-600 ${textSizeClasses[size]}`}>\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "names": [], "mappings": ";;;;;AASA,MAAM,iBAAgD,CAAC,EACrD,OAAO,IAAI,EACX,QAAQ,SAAS,EACjB,IAAI,EACJ,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0CAA0C,EAAE,WAAW;;0BACtE,8OAAC;gBACC,WAAW,CAAC,gDAAgD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,EAAE;;;;;;YAEzG,sBACC,8OAAC;gBAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,CAAC,KAAK,EAAE;0BACxD;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 933, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/index.ts"], "sourcesContent": ["export { default as Layout } from './Layout';\nexport { default as ProductCard } from './ProductCard';\nexport { default as CategoryCard } from './CategoryCard';\nexport { default as VerificationNotice } from './VerificationNotice';\nexport { default as CsrfToken } from './CsrfToken';\nexport { default as StripePaymentForm } from './StripePaymentForm';\nexport { default as LoadingSpinner } from './LoadingSpinner';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 999, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/HomePage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  ProductService,\r\n  CategoryService,\r\n  Product,\r\n  Category,\r\n} from \"../services\";\r\nimport { initializeApi } from \"../services/api\";\r\nimport { ProductCard, CategoryCard, LoadingSpinner } from \"../components\";\r\n\r\nconst HomePage: React.FC = () => {\r\n  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        // Initialize API first\r\n        await initializeApi();\r\n\r\n        const [productsData, categoriesData] = await Promise.all([\r\n          ProductService.getFeaturedProducts(),\r\n          CategoryService.getAllCategories(),\r\n        ]);\r\n\r\n        // Safely access the data arrays\r\n        if (productsData && productsData.data) {\r\n          setFeaturedProducts(\r\n            Array.isArray(productsData.data) ? productsData.data : []\r\n          );\r\n        }\r\n\r\n        if (categoriesData && categoriesData.data) {\r\n          setCategories(\r\n            Array.isArray(categoriesData.data) ? categoriesData.data : []\r\n          );\r\n        }\r\n\r\n        setError(null);\r\n      } catch (err: any) {\r\n        setError(\"Failed to load data. Please try again later.\");\r\n        setFeaturedProducts([]);\r\n        setCategories([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"flex justify-center items-center h-64\">\r\n        <LoadingSpinner size=\"lg\" text=\"Loading products and categories...\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"bg-red-50 border-l-4 border-red-400 p-4 my-4\">\r\n        <div className=\"flex\">\r\n          <div className=\"flex-shrink-0\">\r\n            <svg\r\n              className=\"h-5 w-5 text-red-400\"\r\n              viewBox=\"0 0 20 20\"\r\n              fill=\"currentColor\"\r\n            >\r\n              <path\r\n                fillRule=\"evenodd\"\r\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\r\n                clipRule=\"evenodd\"\r\n              />\r\n            </svg>\r\n          </div>\r\n          <div className=\"ml-3\">\r\n            <p className=\"text-sm text-red-700\">{error}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      {/* Hero section */}\r\n      <div className=\"bg-indigo-700 rounded-lg shadow-xl overflow-hidden\">\r\n        <div className=\"px-4 py-12 sm:px-6 lg:px-8 lg:py-16\">\r\n          <div className=\"max-w-2xl mx-auto text-center\">\r\n            <h1 className=\"text-3xl font-extrabold text-white sm:text-4xl\">\r\n              <span className=\"block\">Welcome to EcommerceApp</span>\r\n            </h1>\r\n            <p className=\"mt-4 text-lg leading-6 text-indigo-100\">\r\n              Discover amazing products at great prices. Shop now and enjoy fast\r\n              delivery!\r\n            </p>\r\n            <div className=\"mt-8 flex justify-center\">\r\n              <div className=\"inline-flex rounded-md shadow\">\r\n                <Link\r\n                  href=\"/products\"\r\n                  className=\"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50\"\r\n                >\r\n                  Browse Products\r\n                </Link>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Featured products section */}\r\n      <div className=\"mt-12\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">\r\n            Featured Products\r\n          </h2>\r\n          <Link\r\n            href=\"/products\"\r\n            className=\"text-indigo-600 hover:text-indigo-500\"\r\n          >\r\n            View all\r\n          </Link>\r\n        </div>\r\n        {featuredProducts.length > 0 ? (\r\n          <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4\">\r\n            {featuredProducts.map((product) => (\r\n              <ProductCard key={product.id} product={product} />\r\n            ))}\r\n          </div>\r\n        ) : loading ? (\r\n          <div className=\"flex justify-center py-8\">\r\n            <LoadingSpinner size=\"md\" text=\"Loading featured products...\" />\r\n          </div>\r\n        ) : (\r\n          <p className=\"text-gray-500\">No featured products available.</p>\r\n        )}\r\n      </div>\r\n\r\n      {/* Categories section */}\r\n      <div className=\"mt-12\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900\">Shop by Category</h2>\r\n          <Link\r\n            href=\"/categories\"\r\n            className=\"text-indigo-600 hover:text-indigo-500\"\r\n          >\r\n            View all\r\n          </Link>\r\n        </div>\r\n        {categories.length > 0 ? (\r\n          <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3\">\r\n            {categories.slice(0, 3).map((category) => (\r\n              <CategoryCard key={category.id} category={category} />\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <p className=\"text-gray-500\">No categories available.</p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAMA;AACA;AAAA;AAAA;AAAA;AAXA;;;;;;;AAaA,MAAM,WAAqB;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,WAAW;gBAEX,uBAAuB;gBACvB,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD;gBAElB,MAAM,CAAC,cAAc,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;oBACvD,kLAAA,CAAA,iBAAc,CAAC,mBAAmB;oBAClC,oLAAA,CAAA,kBAAe,CAAC,gBAAgB;iBACjC;gBAED,gCAAgC;gBAChC,IAAI,gBAAgB,aAAa,IAAI,EAAE;oBACrC,oBACE,MAAM,OAAO,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,GAAG,EAAE;gBAE7D;gBAEA,IAAI,kBAAkB,eAAe,IAAI,EAAE;oBACzC,cACE,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;gBAEjE;gBAEA,SAAS;YACX,EAAE,OAAO,KAAU;gBACjB,SAAS;gBACT,oBAAoB,EAAE;gBACtB,cAAc,EAAE;YAClB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iLAAA,CAAA,iBAAc;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAGrC;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,8OAAC;;0BAEC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAItD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;oBAIF,iBAAiB,MAAM,GAAG,kBACzB,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,2KAAA,CAAA,cAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;+BAG9B,wBACF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iLAAA,CAAA,iBAAc;4BAAC,MAAK;4BAAK,MAAK;;;;;;;;;;6CAGjC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;oBAIF,WAAW,MAAM,GAAG,kBACnB,8OAAC;wBAAI,WAAU;kCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC3B,8OAAC,6KAAA,CAAA,eAAY;gCAAmB,UAAU;+BAAvB,SAAS,EAAE;;;;;;;;;6CAIlC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;uCAEe", "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/LoginPage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { AuthService } from '../services';\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      await AuthService.login({ email, password });\r\n      navigate('/');\r\n    } catch (err: any) {\r\n      console.error('Login error:', err);\r\n      setError(err.response?.data?.message || 'Invalid email or password');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Sign in to your account</h2>\r\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\r\n          Or{' '}\r\n          <Link to=\"/register\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\r\n            create a new account\r\n          </Link>\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\r\n          {error && (\r\n            <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\r\n              <div className=\"flex\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm text-red-700\">{error}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n            <div>\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${\r\n                  loading ? 'opacity-70 cursor-not-allowed' : ''\r\n                }`}\r\n              >\r\n                {loading ? 'Signing in...' : 'Sign in'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,YAAsB;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,4KAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAC1C,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,8OAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,8OAAC,uJAAA,CAAA,OAAI;gCAAC,IAAG;gCAAY,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMvF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,+NAA+N,EACzO,UAAU,kCAAkC,IAC5C;kDAED,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;uCAEe", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/RegisterPage.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { AuthService } from '../services';\n\nconst RegisterPage: React.FC = () => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [passwordConfirmation, setPasswordConfirmation] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const [errors, setErrors] = useState<Record<string, string[]>>({});\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (password !== passwordConfirmation) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError(null);\n      setErrors({});\n      \n      await AuthService.register({\n        name,\n        email,\n        password,\n        password_confirmation: passwordConfirmation,\n      });\n      \n      navigate('/');\n    } catch (err: any) {\n      console.error('Registration error:', err);\n      \n      if (err.response?.data?.errors) {\n        setErrors(err.response.data.errors);\n      } else {\n        setError(err.response?.data?.message || 'Registration failed. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Create a new account</h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link to=\"/login\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n            sign in to your existing account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          {error && (\n            <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm text-red-700\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Name\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  autoComplete=\"name\"\n                  required\n                  value={name}\n                  onChange={(e) => setName(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.name && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.name[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.email ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.email && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.email[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.password ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.password && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.password[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password_confirmation\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password_confirmation\"\n                  name=\"password_confirmation\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={passwordConfirmation}\n                  onChange={(e) => setPasswordConfirmation(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${\n                  loading ? 'opacity-70 cursor-not-allowed' : ''\n                }`}\n              >\n                {loading ? 'Creating account...' : 'Create account'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,eAAyB;IAC7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,aAAa,sBAAsB;YACrC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,UAAU,CAAC;YAEX,MAAM,4KAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBACzB;gBACA;gBACA;gBACA,uBAAuB;YACzB;YAEA,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YAErC,IAAI,IAAI,QAAQ,EAAE,MAAM,QAAQ;gBAC9B,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;YACpC,OAAO;gBACL,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,8OAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,8OAAC,uJAAA,CAAA,OAAI;gCAAC,IAAG;gCAAS,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMpF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,WAAW,CAAC,8CAA8C,EACxD,OAAO,IAAI,GAAG,mBAAmB,kBAClC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,IAAI,kBACV,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAW,CAAC,8CAA8C,EACxD,OAAO,KAAK,GAAG,mBAAmB,kBACnC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,KAAK,kBACX,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAK/D,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAW,CAAC,8CAA8C,EACxD,OAAO,QAAQ,GAAG,mBAAmB,kBACtC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,QAAQ,kBACd,8OAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAKlE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAwB,WAAU;sDAA0C;;;;;;sDAG3F,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;gDACvD,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,+NAA+N,EACzO,UAAU,kCAAkC,IAC5C;kDAED,UAAU,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}]}