{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/login/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { AuthService } from '../../services';\r\n\r\nconst LoginPage: React.FC = () => {\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      await AuthService.login({ email, password });\r\n\r\n      // Manually dispatch both a storage event and a custom event to notify components\r\n      // The storage event only works across tabs, while our custom event works within the same tab\r\n      window.dispatchEvent(new Event('storage'));\r\n      window.dispatchEvent(new Event('storage-update'));\r\n\r\n      // Redirect to home page or profile page based on verification status\r\n      // The verification notice will be shown in the layout regardless\r\n      router.push('/');\r\n    } catch (err: any) {\r\n      console.error('Login error:', err);\r\n\r\n      // Check if this is an email verification error\r\n      if (err.response?.data?.email_verified === false) {\r\n        setError('Email not verified. Please check your email for verification link or go to dashboard to resend it.');\r\n      } else {\r\n        setError(err.response?.data?.message || 'Invalid email or password');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\r\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Sign in to your account</h2>\r\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\r\n          Or{' '}\r\n          <Link href=\"/register\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\r\n            create a new account\r\n          </Link>\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\r\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\r\n          {error && (\r\n            <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\r\n              <div className=\"flex\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <div className=\"ml-3\">\r\n                  <p className=\"text-sm text-red-700\">{error}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\r\n            <div>\r\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\r\n                Email address\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  autoComplete=\"email\"\r\n                  required\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\r\n                Password\r\n              </label>\r\n              <div className=\"mt-1\">\r\n                <input\r\n                  id=\"password\"\r\n                  name=\"password\"\r\n                  type=\"password\"\r\n                  autoComplete=\"current-password\"\r\n                  required\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <button\r\n                type=\"submit\"\r\n                disabled={loading}\r\n                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${\r\n                  loading ? 'opacity-70 cursor-not-allowed' : ''\r\n                }`}\r\n              >\r\n                {loading ? 'Signing in...' : 'Sign in'}\r\n              </button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,YAAsB;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,4KAAA,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAE1C,iFAAiF;YACjF,6FAA6F;YAC7F,OAAO,aAAa,CAAC,IAAI,MAAM;YAC/B,OAAO,aAAa,CAAC,IAAI,MAAM;YAE/B,qEAAqE;YACrE,iEAAiE;YACjE,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gBAAgB;YAE9B,+CAA+C;YAC/C,IAAI,IAAI,QAAQ,EAAE,MAAM,mBAAmB,OAAO;gBAChD,SAAS;YACX,OAAO;gBACL,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,8OAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMzF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,8OAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,+NAA+N,EACzO,UAAU,kCAAkC,IAC5C;kDAED,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;uCAEe", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}