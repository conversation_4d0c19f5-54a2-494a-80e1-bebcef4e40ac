{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/sanctum/csrf-cookie/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst BACKEND_URL = 'http://127.0.0.1:8000';\n\nexport async function GET(request: NextRequest) {\n  try {\n    console.log('[CSRF] Fetching CSRF cookie from backend...');\n    \n    // Forward cookies from the request\n    const cookies = request.headers.get('cookie');\n    const headers: HeadersInit = {\n      'Accept': 'application/json',\n      'Content-Type': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest',\n    };\n\n    if (cookies) {\n      headers['Cookie'] = cookies;\n    }\n\n    // Make the request to the backend\n    const response = await fetch(`${BACKEND_URL}/sanctum/csrf-cookie`, {\n      method: 'GET',\n      headers,\n      credentials: 'include',\n    });\n    \n    console.log(`[CSRF] Backend response status: ${response.status}`);\n\n    // Get response data\n    const responseData = await response.text();\n    \n    // Create the response\n    const nextResponse = new NextResponse(responseData, {\n      status: response.status,\n      statusText: response.statusText,\n    });\n\n    // Forward response headers (especially Set-Cookie)\n    response.headers.forEach((value, key) => {\n      // Skip headers that Next.js handles automatically\n      if (!['content-encoding', 'content-length', 'transfer-encoding'].includes(key.toLowerCase())) {\n        nextResponse.headers.set(key, value);\n      }\n    });\n\n    // Set CORS headers\n    nextResponse.headers.set('Access-Control-Allow-Origin', 'http://localhost:3000');\n    nextResponse.headers.set('Access-Control-Allow-Credentials', 'true');\n    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');\n    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token');\n\n    return nextResponse;\n  } catch (error: any) {\n    console.error('[CSRF] Error:', error);\n    return NextResponse.json(\n      { error: 'CSRF request failed', details: error.message },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': 'http://localhost:3000',\n      'Access-Control-Allow-Credentials': 'true',\n      'Access-Control-Allow-Methods': 'GET, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AAEb,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,mCAAmC;QACnC,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;QACpC,MAAM,UAAuB;YAC3B,UAAU;YACV,gBAAgB;YAChB,oBAAoB;QACtB;QAEA,IAAI,SAAS;YACX,OAAO,CAAC,SAAS,GAAG;QACtB;QAEA,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,GAAG,YAAY,oBAAoB,CAAC,EAAE;YACjE,QAAQ;YACR;YACA,aAAa;QACf;QAEA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,SAAS,MAAM,EAAE;QAEhE,oBAAoB;QACpB,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,sBAAsB;QACtB,MAAM,eAAe,IAAI,gIAAA,CAAA,eAAY,CAAC,cAAc;YAClD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;QACjC;QAEA,mDAAmD;QACnD,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YAC/B,kDAAkD;YAClD,IAAI,CAAC;gBAAC;gBAAoB;gBAAkB;aAAoB,CAAC,QAAQ,CAAC,IAAI,WAAW,KAAK;gBAC5F,aAAa,OAAO,CAAC,GAAG,CAAC,KAAK;YAChC;QACF;QAEA,mBAAmB;QACnB,aAAa,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACxD,aAAa,OAAO,CAAC,GAAG,CAAC,oCAAoC;QAC7D,aAAa,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACzD,aAAa,OAAO,CAAC,GAAG,CAAC,gCAAgC;QAEzD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAuB,SAAS,MAAM,OAAO;QAAC,GACvD;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,oCAAoC;YACpC,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}