import axios from 'axios';

// Create an Axios instance with the base configuration
const api = axios.create({
    // Use relative URLs to leverage the custom API proxy route
    // This ensures requests go through the Next.js proxy at /api/[...proxy]
    baseURL: '/api',
    withCredentials: true, // This is important for cookies to be sent
    headers: {
        'X-Requested-With': 'XMLHttpRequest', // Required for <PERSON><PERSON> to identify AJAX requests
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }
});

// Function to fetch CSRF token
const fetchCSRFToken = async () => {
    try {
        console.log('Attempting to fetch CSRF token...');
        // Use a separate axios instance for Sanctum routes
        const response = await axios.get('/sanctum/csrf-cookie', {
            withCredentials: true,
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            }
        });
        console.log('CSRF token fetched successfully', response.status);
        return true;
    } catch (error: any) {
        console.error('Failed to fetch CSRF token:', error);
        console.error('CSRF Error details:', error.response?.data || error.message);
        return false;
    }
};

// Add a request interceptor to include the auth token in all requests
api.interceptors.request.use(
    config => {
        const token = localStorage.getItem('token');
        if (token) {
            config.headers['Authorization'] = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// Add a response interceptor to handle CSRF token errors
api.interceptors.response.use(
    response => response,
    error => {
        if (error.response && error.response.status === 419) {
            console.error('CSRF token mismatch. Refreshing token...');
            // You could implement token refresh logic here if needed
        }
        return Promise.reject(error);
    }
);

// Initialize API by fetching CSRF token
export const initializeApi = async () => {
    try {
        console.log('Starting API initialization...');
        const csrfSuccess = await fetchCSRFToken();
        if (csrfSuccess) {
            console.log('API initialized successfully');
        } else {
            console.log('API initialization completed with CSRF warning (continuing anyway)');
        }
        return true;
    } catch (error: any) {
        console.error('Failed to initialize API:', error);
        console.error('API Init Error details:', error.response?.data || error.message);
        return false;
    }
};

export default api;
