{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/sanctum/%5B...proxy%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nconst BACKEND_URL = 'http://127.0.0.1:8000';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { proxy: string[] } }\n) {\n  return handleRequest(request, params, 'GET');\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: { proxy: string[] } }\n) {\n  return handleRequest(request, params, 'POST');\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { proxy: string[] } }\n) {\n  return handleRequest(request, params, 'PUT');\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { proxy: string[] } }\n) {\n  return handleRequest(request, params, 'DELETE');\n}\n\nexport async function PATCH(\n  request: NextRequest,\n  { params }: { params: { proxy: string[] } }\n) {\n  return handleRequest(request, params, 'PATCH');\n}\n\nasync function handleRequest(\n  request: NextRequest,\n  params: { proxy: string[] },\n  method: string\n) {\n  try {\n    const path = params.proxy.join('/');\n    const url = `${BACKEND_URL}/sanctum/${path}`;\n\n    // Get search params from the original request\n    const searchParams = request.nextUrl.searchParams.toString();\n    const fullUrl = searchParams ? `${url}?${searchParams}` : url;\n\n    // Prepare headers\n    const headers: HeadersInit = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      'X-Requested-With': 'XMLHttpRequest',\n    };\n\n    // Forward authorization header if present\n    const authHeader = request.headers.get('authorization');\n    if (authHeader) {\n      headers['Authorization'] = authHeader;\n    }\n\n    // Forward cookies\n    const cookies = request.headers.get('cookie');\n    if (cookies) {\n      headers['Cookie'] = cookies;\n    }\n\n    // Prepare request options\n    const requestOptions: RequestInit = {\n      method,\n      headers,\n      credentials: 'include',\n    };\n\n    // Add body for POST, PUT, PATCH requests\n    if (['POST', 'PUT', 'PATCH'].includes(method)) {\n      try {\n        const body = await request.text();\n        if (body) {\n          requestOptions.body = body;\n        }\n      } catch (error) {\n        console.error('Error reading request body:', error);\n      }\n    }\n\n    // Make the request to the backend\n    const response = await fetch(fullUrl, requestOptions);\n\n    // Get response data\n    const responseData = await response.text();\n    \n    // Create the response\n    const nextResponse = new NextResponse(responseData, {\n      status: response.status,\n      statusText: response.statusText,\n    });\n\n    // Forward response headers\n    response.headers.forEach((value, key) => {\n      // Skip headers that Next.js handles automatically\n      if (!['content-encoding', 'content-length', 'transfer-encoding'].includes(key.toLowerCase())) {\n        nextResponse.headers.set(key, value);\n      }\n    });\n\n    // Set CORS headers\n    nextResponse.headers.set('Access-Control-Allow-Origin', 'http://localhost:3000');\n    nextResponse.headers.set('Access-Control-Allow-Credentials', 'true');\n    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');\n    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token');\n\n    return nextResponse;\n  } catch (error: any) {\n    return NextResponse.json(\n      { error: 'Sanctum proxy request failed', details: error.message },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': 'http://localhost:3000',\n      'Access-Control-Allow-Credentials': 'true',\n      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',\n      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, Accept, X-CSRF-Token',\n    },\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,MAAM,cAAc;AAEb,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAmC;IAE3C,OAAO,cAAc,SAAS,QAAQ;AACxC;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAmC;IAE3C,OAAO,cAAc,SAAS,QAAQ;AACxC;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAmC;IAE3C,OAAO,cAAc,SAAS,QAAQ;AACxC;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAAmC;IAE3C,OAAO,cAAc,SAAS,QAAQ;AACxC;AAEO,eAAe,MACpB,OAAoB,EACpB,EAAE,MAAM,EAAmC;IAE3C,OAAO,cAAc,SAAS,QAAQ;AACxC;AAEA,eAAe,cACb,OAAoB,EACpB,MAA2B,EAC3B,MAAc;IAEd,IAAI;QACF,MAAM,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC;QAC/B,MAAM,MAAM,GAAG,YAAY,SAAS,EAAE,MAAM;QAE5C,8CAA8C;QAC9C,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY,CAAC,QAAQ;QAC1D,MAAM,UAAU,eAAe,GAAG,IAAI,CAAC,EAAE,cAAc,GAAG;QAE1D,kBAAkB;QAClB,MAAM,UAAuB;YAC3B,gBAAgB;YAChB,UAAU;YACV,oBAAoB;QACtB;QAEA,0CAA0C;QAC1C,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QACvC,IAAI,YAAY;YACd,OAAO,CAAC,gBAAgB,GAAG;QAC7B;QAEA,kBAAkB;QAClB,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;QACpC,IAAI,SAAS;YACX,OAAO,CAAC,SAAS,GAAG;QACtB;QAEA,0BAA0B;QAC1B,MAAM,iBAA8B;YAClC;YACA;YACA,aAAa;QACf;QAEA,yCAAyC;QACzC,IAAI;YAAC;YAAQ;YAAO;SAAQ,CAAC,QAAQ,CAAC,SAAS;YAC7C,IAAI;gBACF,MAAM,OAAO,MAAM,QAAQ,IAAI;gBAC/B,IAAI,MAAM;oBACR,eAAe,IAAI,GAAG;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;QAEA,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,SAAS;QAEtC,oBAAoB;QACpB,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,sBAAsB;QACtB,MAAM,eAAe,IAAI,gIAAA,CAAA,eAAY,CAAC,cAAc;YAClD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;QACjC;QAEA,2BAA2B;QAC3B,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;YAC/B,kDAAkD;YAClD,IAAI,CAAC;gBAAC;gBAAoB;gBAAkB;aAAoB,CAAC,QAAQ,CAAC,IAAI,WAAW,KAAK;gBAC5F,aAAa,OAAO,CAAC,GAAG,CAAC,KAAK;YAChC;QACF;QAEA,mBAAmB;QACnB,aAAa,OAAO,CAAC,GAAG,CAAC,+BAA+B;QACxD,aAAa,OAAO,CAAC,GAAG,CAAC,oCAAoC;QAC7D,aAAa,OAAO,CAAC,GAAG,CAAC,gCAAgC;QACzD,aAAa,OAAO,CAAC,GAAG,CAAC,gCAAgC;QAEzD,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;YAAgC,SAAS,MAAM,OAAO;QAAC,GAChE;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,oCAAoC;YACpC,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}