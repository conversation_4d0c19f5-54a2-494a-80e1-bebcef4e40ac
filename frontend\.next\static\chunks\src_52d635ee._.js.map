{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Create an Axios instance with the base configuration\r\nconst api = axios.create({\r\n    // Use relative URLs to leverage the custom API proxy route\r\n    // This ensures requests go through the Next.js proxy at /api/[...proxy]\r\n    baseURL: '/api',\r\n    withCredentials: true, // This is important for cookies to be sent\r\n    headers: {\r\n        'X-Requested-With': 'XMLHttpRequest', // Required for <PERSON><PERSON> to identify AJAX requests\r\n        'Content-Type': 'application/json',\r\n        'Accept': 'application/json',\r\n    }\r\n});\r\n\r\n// Function to fetch CSRF token\r\nconst fetchCSRFToken = async () => {\r\n    try {\r\n        // Use a separate axios instance for Sanctum routes\r\n        await axios.get('/sanctum/csrf-cookie', {\r\n            withCredentials: true,\r\n            headers: {\r\n                'Accept': 'application/json',\r\n                'Content-Type': 'application/json',\r\n            }\r\n        });\r\n        return true;\r\n    } catch (error: any) {\r\n        return false;\r\n    }\r\n};\r\n\r\n// Add a request interceptor to include the auth token in all requests\r\napi.interceptors.request.use(\r\n    config => {\r\n        const token = localStorage.getItem('token');\r\n        if (token) {\r\n            config.headers['Authorization'] = `Bearer ${token}`;\r\n        }\r\n        return config;\r\n    },\r\n    error => {\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Add a response interceptor to handle CSRF token errors\r\napi.interceptors.response.use(\r\n    response => response,\r\n    error => {\r\n        if (error.response && error.response.status === 419) {\r\n            console.error('CSRF token mismatch. Refreshing token...');\r\n            // You could implement token refresh logic here if needed\r\n        }\r\n        return Promise.reject(error);\r\n    }\r\n);\r\n\r\n// Initialize API by fetching CSRF token\r\nexport const initializeApi = async () => {\r\n    try {\r\n        await fetchCSRFToken();\r\n        return true;\r\n    } catch (error: any) {\r\n        return false;\r\n    }\r\n};\r\n\r\nexport default api;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,uDAAuD;AACvD,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACrB,2DAA2D;IAC3D,wEAAwE;IACxE,SAAS;IACT,iBAAiB;IACjB,SAAS;QACL,oBAAoB;QACpB,gBAAgB;QAChB,UAAU;IACd;AACJ;AAEA,+BAA+B;AAC/B,MAAM,iBAAiB;IACnB,IAAI;QACA,mDAAmD;QACnD,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,wBAAwB;YACpC,iBAAiB;YACjB,SAAS;gBACL,UAAU;gBACV,gBAAgB;YACpB;QACJ;QACA,OAAO;IACX,EAAE,OAAO,OAAY;QACjB,OAAO;IACX;AACJ;AAEA,sEAAsE;AACtE,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CACxB,CAAA;IACI,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACP,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvD;IACA,OAAO;AACX,GACA,CAAA;IACI,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAGJ,yDAAyD;AACzD,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzB,CAAA,WAAY,UACZ,CAAA;IACI,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACjD,QAAQ,KAAK,CAAC;IACd,yDAAyD;IAC7D;IACA,OAAO,QAAQ,MAAM,CAAC;AAC1B;AAIG,MAAM,gBAAgB;IACzB,IAAI;QACA,MAAM;QACN,OAAO;IACX,EAAE,OAAO,OAAY;QACjB,OAAO;IACX;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/auth.service.ts"], "sourcesContent": ["import api from './api';\r\n\r\nexport interface User {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  email_verified_at: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface LoginCredentials {\r\n  email: string;\r\n  password: string;\r\n}\r\n\r\nexport interface RegisterData {\r\n  name: string;\r\n  email: string;\r\n  password: string;\r\n  password_confirmation: string;\r\n}\r\n\r\nexport interface AuthResponse {\r\n  success: boolean;\r\n  message: string;\r\n  data?: {\r\n    user: User;\r\n    token: string;\r\n    email_verified: boolean;\r\n  };\r\n  email_verified?: boolean;\r\n}\r\n\r\nconst AuthService = {\r\n  login: async (credentials: LoginCredentials): Promise<User> => {\r\n    try {\r\n      // First, get the CSRF cookie from Laravel Sanctum\r\n      await api.get('/sanctum/csrf-cookie');\r\n\r\n      // Then make the login request\r\n      const response = await api.post<AuthResponse>('/api/login', credentials);\r\n\r\n      if (response.data.success && response.data.data) {\r\n        const { user, token } = response.data.data;\r\n        localStorage.setItem('token', token);\r\n        localStorage.setItem('user', JSON.stringify(user));\r\n\r\n        // Dispatch both a storage event and a custom event to notify components\r\n        // The storage event only works across tabs, while our custom event works within the same tab\r\n        window.dispatchEvent(new Event('storage'));\r\n        window.dispatchEvent(new Event('storage-update'));\r\n\r\n        return user;\r\n      } else {\r\n        throw new Error(response.data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Login error details:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  register: async (data: RegisterData): Promise<User> => {\r\n    try {\r\n      // First, get the CSRF cookie from Laravel Sanctum\r\n      await api.get('/sanctum/csrf-cookie');\r\n\r\n      // Then make the registration request\r\n      // The API route is /api/register because it's in api.php\r\n      const response = await api.post<AuthResponse>('/api/register', data);\r\n\r\n      if (response.data.success && response.data.data) {\r\n        const { user, token } = response.data.data;\r\n        localStorage.setItem('token', token);\r\n        localStorage.setItem('user', JSON.stringify(user));\r\n\r\n        // Dispatch both a storage event and a custom event to notify components\r\n        // The storage event only works across tabs, while our custom event works within the same tab\r\n        window.dispatchEvent(new Event('storage'));\r\n        window.dispatchEvent(new Event('storage-update'));\r\n\r\n        return user;\r\n      } else {\r\n        throw new Error(response.data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Registration error details:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  logout: async (): Promise<void> => {\r\n    try {\r\n      // First, get the CSRF cookie from Laravel Sanctum\r\n      await api.get('/sanctum/csrf-cookie');\r\n\r\n      // Then make the logout request\r\n      await api.post('/api/logout');\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('user');\r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n      // Still remove items even if API call fails\r\n      localStorage.removeItem('token');\r\n      localStorage.removeItem('user');\r\n    }\r\n  },\r\n\r\n  getCurrentUser: (): User | null => {\r\n    const userStr = localStorage.getItem('user');\r\n    if (userStr) {\r\n      return JSON.parse(userStr);\r\n    }\r\n    return null;\r\n  },\r\n\r\n  isAuthenticated: (): boolean => {\r\n    return !!localStorage.getItem('token');\r\n  },\r\n\r\n  isEmailVerified: (): boolean => {\r\n    const userStr = localStorage.getItem('user');\r\n    if (userStr) {\r\n      const user = JSON.parse(userStr) as User;\r\n      return !!user.email_verified_at;\r\n    }\r\n    return false;\r\n  },\r\n\r\n  resendVerificationEmail: async (): Promise<void> => {\r\n    try {\r\n      // Check if user is authenticated\r\n      if (!AuthService.isAuthenticated()) {\r\n        throw new Error('You must be logged in to resend verification email');\r\n      }\r\n\r\n      console.log('Attempting to resend verification email...');\r\n\r\n      // First, get the CSRF cookie from Laravel Sanctum\r\n      const csrfResponse = await api.get('/sanctum/csrf-cookie');\r\n      console.log('CSRF cookie response:', csrfResponse.status);\r\n\r\n      // Then make the request to resend verification email\r\n      console.log('Sending verification email request...');\r\n      const response = await api.post('/api/email/verification-notification');\r\n      console.log('Verification email response:', response.status, response.data);\r\n\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Resend verification email error:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  refreshUserData: async (): Promise<User | null> => {\r\n    try {\r\n      // Check if user is authenticated\r\n      if (!AuthService.isAuthenticated()) {\r\n        return null;\r\n      }\r\n\r\n      // Get user data from the API\r\n      const response = await api.get('/api/user');\r\n      console.log('Refresh user data response:', response.data);\r\n\r\n      if (response.data && response.data.success && response.data.data && response.data.data.user) {\r\n        const userData = response.data.data.user;\r\n\r\n        // Update the user data in localStorage\r\n        localStorage.setItem('user', JSON.stringify(userData));\r\n\r\n        // Dispatch both a storage event and a custom event to notify components\r\n        // The storage event only works across tabs, while our custom event works within the same tab\r\n        window.dispatchEvent(new Event('storage'));\r\n        window.dispatchEvent(new Event('storage-update'));\r\n\r\n        return userData;\r\n      }\r\n\r\n      return null;\r\n    } catch (error) {\r\n      console.error('Refresh user data error:', error);\r\n      return null;\r\n    }\r\n  },\r\n};\r\n\r\nexport default AuthService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAkCA,MAAM,cAAc;IAClB,OAAO,OAAO;QACZ,IAAI;YACF,kDAAkD;YAClD,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;YAEd,8BAA8B;YAC9B,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAe,cAAc;YAE5D,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAC1C,aAAa,OAAO,CAAC,SAAS;gBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAE5C,wEAAwE;gBACxE,6FAA6F;gBAC7F,OAAO,aAAa,CAAC,IAAI,MAAM;gBAC/B,OAAO,aAAa,CAAC,IAAI,MAAM;gBAE/B,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,UAAU,OAAO;QACf,IAAI;YACF,kDAAkD;YAClD,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;YAEd,qCAAqC;YACrC,yDAAyD;YACzD,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAe,iBAAiB;YAE/D,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,EAAE;gBAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI,CAAC,IAAI;gBAC1C,aAAa,OAAO,CAAC,SAAS;gBAC9B,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAE5C,wEAAwE;gBACxE,6FAA6F;gBAC7F,OAAO,aAAa,CAAC,IAAI,MAAM;gBAC/B,OAAO,aAAa,CAAC,IAAI,MAAM;gBAE/B,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO;YACvC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,QAAQ;QACN,IAAI;YACF,kDAAkD;YAClD,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;YAEd,+BAA+B;YAC/B,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAC;YACf,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,4CAA4C;YAC5C,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,gBAAgB;QACd,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,SAAS;YACX,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;IACT;IAEA,iBAAiB;QACf,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;IAEA,iBAAiB;QACf,MAAM,UAAU,aAAa,OAAO,CAAC;QACrC,IAAI,SAAS;YACX,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,OAAO,CAAC,CAAC,KAAK,iBAAiB;QACjC;QACA,OAAO;IACT;IAEA,yBAAyB;QACvB,IAAI;YACF,iCAAiC;YACjC,IAAI,CAAC,YAAY,eAAe,IAAI;gBAClC,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC;YAEZ,kDAAkD;YAClD,MAAM,eAAe,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;YACnC,QAAQ,GAAG,CAAC,yBAAyB,aAAa,MAAM;YAExD,qDAAqD;YACrD,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAC;YAChC,QAAQ,GAAG,CAAC,gCAAgC,SAAS,MAAM,EAAE,SAAS,IAAI;YAE1E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,iBAAiB;QACf,IAAI;YACF,iCAAiC;YACjC,IAAI,CAAC,YAAY,eAAe,IAAI;gBAClC,OAAO;YACT;YAEA,6BAA6B;YAC7B,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;YAC/B,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI;YAExD,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC3F,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI;gBAExC,uCAAuC;gBACvC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;gBAE5C,wEAAwE;gBACxE,6FAA6F;gBAC7F,OAAO,aAAa,CAAC,IAAI,MAAM;gBAC/B,OAAO,aAAa,CAAC,IAAI,MAAM;gBAE/B,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/product.service.ts"], "sourcesContent": ["import api from './api';\r\n\r\nexport interface Product {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description: string;\r\n  price: number;\r\n  quantity: number;\r\n  image: string | null;\r\n  category_id: number;\r\n  is_active: boolean;\r\n  featured: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  category?: Category;\r\n}\r\n\r\nexport interface Category {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description: string | null;\r\n  image: string | null;\r\n  is_active: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface ProductResponse {\r\n  success: boolean;\r\n  data: Product | Product[];\r\n  message?: string;\r\n}\r\n\r\nexport const fetchProducts = async (): Promise<ProductResponse> => {\r\n  try {\r\n    const response = await api.get<ProductResponse>('/products');\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getFeaturedProducts = async (): Promise<ProductResponse> => {\r\n  try {\r\n    const response = await api.get<ProductResponse>('/products?featured=true');\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getProductBySlug = async (slug: string): Promise<Product> => {\r\n  try {\r\n    // First get all products\r\n    const response = await api.get<ProductResponse>('/products');\r\n    const products = response.data.data as Product[];\r\n\r\n    // Find the product with the matching slug\r\n    const product = products.find(p => p.slug === slug);\r\n\r\n    if (!product) {\r\n      throw new Error('Product not found');\r\n    }\r\n\r\n    return product;\r\n  } catch (error) {\r\n    console.error('Error fetching product by slug:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getAllProducts = async (): Promise<Product[]> => {\r\n  try {\r\n    const response = await api.get<ProductResponse>('/products');\r\n    return response.data.data as Product[];\r\n  } catch (error) {\r\n    console.error('Error fetching all products:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst ProductService = {\r\n  fetchProducts,\r\n  getFeaturedProducts,\r\n  getProductBySlug,\r\n  getAllProducts\r\n};\r\n\r\nexport default ProductService;\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAmCO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAkB;QAChD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAkB;QAChD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,yBAAyB;QACzB,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAkB;QAChD,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;QAEnC,0CAA0C;QAC1C,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAE9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAkB;QAChD,OAAO,SAAS,IAAI,CAAC,IAAI;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEA,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/category.service.ts"], "sourcesContent": ["import api from './api';\r\nimport { Category } from './product.service';\r\n\r\nexport interface CategoryResponse {\r\n  success: boolean;\r\n  data: Category | Category[];\r\n  message?: string;\r\n}\r\n\r\nexport const getAllCategories = async (): Promise<CategoryResponse> => {\r\n  try {\r\n    const response = await api.get<CategoryResponse>('/categories');\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getCategoryBySlug = async (slug: string): Promise<Category> => {\r\n  try {\r\n    // First get all categories\r\n    const response = await api.get<CategoryResponse>('/categories');\r\n    const categories = response.data.data as Category[];\r\n\r\n    // Find the category with the matching slug\r\n    const category = categories.find(c => c.slug === slug);\r\n\r\n    if (!category) {\r\n      throw new Error('Category not found');\r\n    }\r\n\r\n    return category;\r\n  } catch (error) {\r\n    console.error('Error fetching category by slug:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst CategoryService = {\r\n  getAllCategories,\r\n  getCategoryBySlug\r\n};\r\n\r\nexport default CategoryService;\r\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,mBAAmB;IAC9B,IAAI;QACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAmB;QACjD,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,2BAA2B;QAC3B,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAmB;QACjD,MAAM,aAAa,SAAS,IAAI,CAAC,IAAI;QAErC,2CAA2C;QAC3C,MAAM,WAAW,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAEjD,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM;IACR;AACF;AAEA,MAAM,kBAAkB;IACtB;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/order.service.ts"], "sourcesContent": ["import api from './api';\r\nimport { Product } from './product.service';\r\n\r\nexport interface OrderItem {\r\n  id: number;\r\n  order_id: number;\r\n  product_id: number;\r\n  quantity: number;\r\n  price: number;\r\n  subtotal: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  product?: Product;\r\n}\r\n\r\nexport interface Order {\r\n  id: number;\r\n  user_id: number;\r\n  total_amount: number;\r\n  status: 'pending' | 'processing' | 'completed' | 'canceled';\r\n  payment_method: 'cash' | 'credit_card' | 'paypal';\r\n  payment_status: 'pending' | 'paid' | 'failed';\r\n  shipping_address: string;\r\n  shipping_city: string;\r\n  shipping_state: string;\r\n  shipping_country: string;\r\n  shipping_zip_code: string;\r\n  shipping_phone: string;\r\n  notes: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  order_items?: OrderItem[];\r\n}\r\n\r\nexport interface OrderResponse {\r\n  success: boolean;\r\n  data: Order | Order[];\r\n  message?: string;\r\n}\r\n\r\nexport interface CartItem {\r\n  product_id: number;\r\n  quantity: number;\r\n  product: Product;\r\n}\r\n\r\nexport interface CreateOrderData {\r\n  shipping_address: string;\r\n  shipping_city: string;\r\n  shipping_state: string;\r\n  shipping_country: string;\r\n  shipping_zip_code: string;\r\n  shipping_phone: string;\r\n  payment_method: 'cash' | 'credit_card' | 'paypal';\r\n  notes?: string;\r\n  items: {\r\n    product_id: number;\r\n    quantity: number;\r\n  }[];\r\n}\r\n\r\nconst OrderService = {\r\n  getAllOrders: async (): Promise<Order[]> => {\r\n    try {\r\n      const response = await api.get<OrderResponse>('/orders');\r\n      return response.data.data as Order[];\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  getOrderById: async (id: number): Promise<Order> => {\r\n    try {\r\n      const response = await api.get<OrderResponse>(`/orders/${id}`);\r\n      return response.data.data as Order;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  createOrder: async (orderData: CreateOrderData): Promise<Order> => {\r\n    try {\r\n      const response = await api.post<OrderResponse>('/orders', orderData);\r\n      return response.data.data as Order;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  updateOrder: async (id: number, notes: string): Promise<Order> => {\r\n    try {\r\n      const response = await api.put<OrderResponse>(`/orders/${id}`, { notes });\r\n      return response.data.data as Order;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  cancelOrder: async (id: number): Promise<Order> => {\r\n    try {\r\n      const response = await api.post<OrderResponse>(`/orders/${id}/cancel`);\r\n      return response.data.data as Order;\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\nexport default OrderService;\r\n"], "names": [], "mappings": ";;;AAAA;;AA6DA,MAAM,eAAe;IACnB,cAAc;QACZ,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAgB;YAC9C,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAgB,CAAC,QAAQ,EAAE,IAAI;YAC7D,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAgB,WAAW;YAC1D,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,aAAa,OAAO,IAAY;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAgB,CAAC,QAAQ,EAAE,IAAI,EAAE;gBAAE;YAAM;YACvE,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAgB,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YACrE,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/cart.service.ts"], "sourcesContent": ["import { Product } from './product.service';\n\nexport interface CartItem {\n  product_id: number;\n  quantity: number;\n  product: Product;\n}\n\nexport interface Cart {\n  items: CartItem[];\n  total: number;\n}\n\nconst CART_STORAGE_KEY = 'ecommerce_cart';\n\nconst CartService = {\n  getCart: (): Cart => {\n    const cartData = localStorage.getItem(CART_STORAGE_KEY);\n    if (cartData) {\n      return JSON.parse(cartData);\n    }\n    return { items: [], total: 0 };\n  },\n\n  saveCart: (cart: Cart): void => {\n    localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));\n  },\n\n  addToCart: (product: Product, quantity: number = 1): Cart => {\n    const cart = CartService.getCart();\n    const existingItemIndex = cart.items.findIndex(item => item.product_id === product.id);\n\n    if (existingItemIndex !== -1) {\n      // Update quantity if item already exists\n      cart.items[existingItemIndex].quantity += quantity;\n    } else {\n      // Add new item\n      cart.items.push({\n        product_id: product.id,\n        quantity,\n        product,\n      });\n    }\n\n    // Recalculate total\n    cart.total = cart.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n    \n    CartService.saveCart(cart);\n    return cart;\n  },\n\n  updateQuantity: (productId: number, quantity: number): Cart => {\n    const cart = CartService.getCart();\n    const itemIndex = cart.items.findIndex(item => item.product_id === productId);\n\n    if (itemIndex !== -1) {\n      if (quantity <= 0) {\n        // Remove item if quantity is 0 or negative\n        cart.items.splice(itemIndex, 1);\n      } else {\n        // Update quantity\n        cart.items[itemIndex].quantity = quantity;\n      }\n\n      // Recalculate total\n      cart.total = cart.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n      \n      CartService.saveCart(cart);\n    }\n\n    return cart;\n  },\n\n  removeFromCart: (productId: number): Cart => {\n    const cart = CartService.getCart();\n    const itemIndex = cart.items.findIndex(item => item.product_id === productId);\n\n    if (itemIndex !== -1) {\n      cart.items.splice(itemIndex, 1);\n      \n      // Recalculate total\n      cart.total = cart.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);\n      \n      CartService.saveCart(cart);\n    }\n\n    return cart;\n  },\n\n  clearCart: (): Cart => {\n    const emptyCart = { items: [], total: 0 };\n    CartService.saveCart(emptyCart);\n    return emptyCart;\n  },\n\n  getItemCount: (): number => {\n    const cart = CartService.getCart();\n    return cart.items.reduce((count, item) => count + item.quantity, 0);\n  },\n};\n\nexport default CartService;\n"], "names": [], "mappings": ";;;AAaA,MAAM,mBAAmB;AAEzB,MAAM,cAAc;IAClB,SAAS;QACP,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,UAAU;YACZ,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;YAAE,OAAO,EAAE;YAAE,OAAO;QAAE;IAC/B;IAEA,UAAU,CAAC;QACT,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACxD;IAEA,WAAW,CAAC,SAAkB,WAAmB,CAAC;QAChD,MAAM,OAAO,YAAY,OAAO;QAChC,MAAM,oBAAoB,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,QAAQ,EAAE;QAErF,IAAI,sBAAsB,CAAC,GAAG;YAC5B,yCAAyC;YACzC,KAAK,KAAK,CAAC,kBAAkB,CAAC,QAAQ,IAAI;QAC5C,OAAO;YACL,eAAe;YACf,KAAK,KAAK,CAAC,IAAI,CAAC;gBACd,YAAY,QAAQ,EAAE;gBACtB;gBACA;YACF;QACF;QAEA,oBAAoB;QACpB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;QAE1F,YAAY,QAAQ,CAAC;QACrB,OAAO;IACT;IAEA,gBAAgB,CAAC,WAAmB;QAClC,MAAM,OAAO,YAAY,OAAO;QAChC,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK;QAEnE,IAAI,cAAc,CAAC,GAAG;YACpB,IAAI,YAAY,GAAG;gBACjB,2CAA2C;gBAC3C,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW;YAC/B,OAAO;gBACL,kBAAkB;gBAClB,KAAK,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG;YACnC;YAEA,oBAAoB;YACpB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;YAE1F,YAAY,QAAQ,CAAC;QACvB;QAEA,OAAO;IACT;IAEA,gBAAgB,CAAC;QACf,MAAM,OAAO,YAAY,OAAO;QAChC,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK;QAEnE,IAAI,cAAc,CAAC,GAAG;YACpB,KAAK,KAAK,CAAC,MAAM,CAAC,WAAW;YAE7B,oBAAoB;YACpB,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,OAAO,CAAC,KAAK,GAAG,KAAK,QAAQ,EAAG;YAE1F,YAAY,QAAQ,CAAC;QACvB;QAEA,OAAO;IACT;IAEA,WAAW;QACT,MAAM,YAAY;YAAE,OAAO,EAAE;YAAE,OAAO;QAAE;QACxC,YAAY,QAAQ,CAAC;QACrB,OAAO;IACT;IAEA,cAAc;QACZ,MAAM,OAAO,YAAY,OAAO;QAChC,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;IACnE;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/stripe.service.ts"], "sourcesContent": ["import api from './api';\n\nexport interface CreatePaymentIntentData {\n  items: {\n    product_id: number;\n    quantity: number;\n  }[];\n}\n\nexport interface CreatePaymentIntentResponse {\n  success: boolean;\n  client_secret: string;\n  amount: number;\n  message?: string;\n}\n\nexport interface ConfirmPaymentData {\n  payment_intent_id: string;\n  shipping_address: string;\n  shipping_city: string;\n  shipping_state: string;\n  shipping_country: string;\n  shipping_zip_code: string;\n  shipping_phone: string;\n  notes?: string;\n}\n\nexport interface ConfirmPaymentResponse {\n  success: boolean;\n  message: string;\n  payment_intent: string;\n  amount: number;\n  items: any[];\n}\n\nconst StripeService = {\n  createPaymentIntent: async (data: CreatePaymentIntentData): Promise<CreatePaymentIntentResponse> => {\n    try {\n      const response = await api.post<CreatePaymentIntentResponse>('/payment/create-intent', data);\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  processPayment: async (paymentMethodId: string, items: { product_id: number; quantity: number }[]): Promise<{ success: boolean; message?: string; payment_intent?: string }> => {\n    try {\n      const response = await api.post('/payment/process', {\n        payment_method_id: paymentMethodId,\n        items: items\n      });\n      return response.data;\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default StripeService;\n"], "names": [], "mappings": ";;;AAAA;;AAmCA,MAAM,gBAAgB;IACpB,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAA8B,0BAA0B;YACvF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO,iBAAyB;QAC9C,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAG,CAAC,IAAI,CAAC,oBAAoB;gBAClD,mBAAmB;gBACnB,OAAO;YACT;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/index.ts"], "sourcesContent": ["export { default as api } from './api';\nexport { default as AuthService } from './auth.service';\nexport { default as ProductService } from './product.service';\nexport { default as CategoryService } from './category.service';\nexport { default as OrderService } from './order.service';\nexport { default as CartService } from './cart.service';\nexport { default as StripeService } from './stripe.service';\n\n// Export types\nexport type { User } from './auth.service';\nexport type { Product, Category } from './product.service';\nexport type { Order, OrderItem, CartItem as OrderCartItem } from './order.service';\nexport type { Cart, CartItem } from './cart.service';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CsrfToken.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport api from '../services/api';\r\n\r\n/**\r\n * Component to fetch CSRF token on initial load\r\n */\r\nexport default function CsrfToken() {\r\n  useEffect(() => {\r\n    // Fetch CSRF token on initial load\r\n    const fetchCsrfToken = async () => {\r\n      try {\r\n        await api.get('/sanctum/csrf-cookie');\r\n        console.log('CSRF token fetched successfully');\r\n      } catch (error) {\r\n        console.error('Failed to fetch CSRF token:', error);\r\n      }\r\n    };\r\n\r\n    fetchCsrfToken();\r\n  }, []);\r\n\r\n  // This component doesn't render anything\r\n  return null;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAQe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,mCAAmC;YACnC,MAAM;sDAAiB;oBACrB,IAAI;wBACF,MAAM,yHAAA,CAAA,UAAG,CAAC,GAAG,CAAC;wBACd,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;gBACF;;YAEA;QACF;8BAAG,EAAE;IAEL,yCAAyC;IACzC,OAAO;AACT;GAjBwB;KAAA", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/VerificationNotice.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { AuthService } from '../services';\n\ninterface VerificationNoticeProps {\n  email: string;\n  showDismiss?: boolean;\n  onDismiss?: () => void;\n}\n\nconst VerificationNotice: React.FC<VerificationNoticeProps> = ({\n  email,\n  showDismiss = false,\n  onDismiss\n}) => {\n  const [sending, setSending] = useState(false);\n  const [sent, setSent] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [dismissed, setDismissed] = useState(false);\n\n  if (dismissed) {\n    return null;\n  }\n\n  const handleResend = async () => {\n    try {\n      setSending(true);\n      setError(null);\n      await AuthService.resendVerificationEmail();\n      setSent(true);\n    } catch (err) {\n      setError('Failed to resend verification email. Please try again.');\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const handleDismiss = () => {\n    setDismissed(true);\n    if (onDismiss) {\n      onDismiss();\n    }\n  };\n\n  return (\n    <div className=\"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6\">\n      <div className=\"flex\">\n        <div className=\"flex-shrink-0\">\n          <svg className=\"h-5 w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        </div>\n        <div className=\"ml-3 flex-grow\">\n          <div className=\"flex justify-between\">\n            <p className=\"text-sm text-yellow-700\">\n              Your email address ({email}) has not been verified. Please check your email for a verification link.\n            </p>\n            {showDismiss && (\n              <button\n                onClick={handleDismiss}\n                className=\"ml-3 flex-shrink-0 text-yellow-500 hover:text-yellow-700\"\n              >\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </button>\n            )}\n          </div>\n          {error && (\n            <p className=\"mt-2 text-sm text-red-600\">{error}</p>\n          )}\n          {sent ? (\n            <p className=\"mt-2 text-sm text-green-600\">\n              A new verification link has been sent to your email address.\n            </p>\n          ) : (\n            <div className=\"mt-2 flex flex-wrap gap-2\">\n              <button\n                onClick={handleResend}\n                disabled={sending}\n                className=\"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 cursor-pointer\"\n              >\n                {sending ? 'Sending...' : 'Resend Verification Email'}\n              </button>\n              <Link\n                href=\"/profile\"\n                className=\"inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\"\n              >\n                Go to My Account\n              </Link>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VerificationNotice;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAYA,MAAM,qBAAwD,CAAC,EAC7D,KAAK,EACL,cAAc,KAAK,EACnB,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,+KAAA,CAAA,cAAW,CAAC,uBAAuB;YACzC,QAAQ;QACV,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI,WAAW;YACb;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAA0B,SAAQ;wBAAY,MAAK;kCAChE,cAAA,6LAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoN,UAAS;;;;;;;;;;;;;;;;8BAG5P,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;wCAA0B;wCAChB;wCAAM;;;;;;;gCAE5B,6BACC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,SAAQ;wCAAY,MAAK;kDAChD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;wBAKhP,uBACC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;wBAE3C,qBACC,6LAAC;4BAAE,WAAU;sCAA8B;;;;;iDAI3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,UAAU,eAAe;;;;;;8CAE5B,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtFM;KAAA;uCAwFS", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/utils/auth-utils.ts"], "sourcesContent": ["import { AuthService } from '../services';\n\n/**\n * Refreshes the user data from the backend and updates localStorage\n * @returns A promise that resolves to the updated user data\n */\nexport const refreshUserData = async () => {\n  try {\n    console.log('Refreshing user data from backend...');\n    const userData = await AuthService.refreshUserData();\n    console.log('User data refreshed successfully:', userData);\n    return userData;\n  } catch (error) {\n    console.error('Failed to refresh user data:', error);\n    \n    // Return the current user data from localStorage as fallback\n    return AuthService.getCurrentUser();\n  }\n};\n\n/**\n * Checks if the user's email is verified\n * @param forceRefresh Whether to force a refresh of user data from the backend\n * @returns A promise that resolves to a boolean indicating if the email is verified\n */\nexport const checkEmailVerified = async (forceRefresh = false) => {\n  if (forceRefresh) {\n    await refreshUserData();\n  }\n  \n  return AuthService.isEmailVerified();\n};\n\n/**\n * Adds event listeners for storage events to update when user data changes\n * @param callback Function to call when storage events occur\n * @returns A cleanup function to remove the event listeners\n */\nexport const addStorageEventListeners = (callback: () => void) => {\n  // Listen for both storage events (for cross-tab updates) and our custom events (for same-tab updates)\n  window.addEventListener('storage', callback);\n  window.addEventListener('storage-update', callback);\n  \n  // Return a cleanup function\n  return () => {\n    window.removeEventListener('storage', callback);\n    window.removeEventListener('storage-update', callback);\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;AAMO,MAAM,kBAAkB;IAC7B,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,+KAAA,CAAA,cAAW,CAAC,eAAe;QAClD,QAAQ,GAAG,CAAC,qCAAqC;QACjD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,6DAA6D;QAC7D,OAAO,+KAAA,CAAA,cAAW,CAAC,cAAc;IACnC;AACF;AAOO,MAAM,qBAAqB,OAAO,eAAe,KAAK;IAC3D,IAAI,cAAc;QAChB,MAAM;IACR;IAEA,OAAO,+KAAA,CAAA,cAAW,CAAC,eAAe;AACpC;AAOO,MAAM,2BAA2B,CAAC;IACvC,sGAAsG;IACtG,OAAO,gBAAgB,CAAC,WAAW;IACnC,OAAO,gBAAgB,CAAC,kBAAkB;IAE1C,4BAA4B;IAC5B,OAAO;QACL,OAAO,mBAAmB,CAAC,WAAW;QACtC,OAAO,mBAAmB,CAAC,kBAAkB;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/client-layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\r\nimport { AuthService, CartService } from '../services';\r\nimport CsrfToken from '../components/CsrfToken';\r\nimport VerificationNotice from '../components/VerificationNotice';\r\nimport { refreshUserData as refreshUserDataUtil, addStorageEventListeners } from '../utils/auth-utils';\r\n\r\ninterface ClientLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function ClientLayout({ children }: ClientLayoutProps) {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n  const [cartItemCount, setCartItemCount] = useState(0);\r\n  const [showVerificationNotice, setShowVerificationNotice] = useState(false);\r\n  const [currentUser, setCurrentUser] = useState<any>(null);\r\n\r\n  // Function to update authentication state from localStorage\r\n  const updateAuthState = () => {\r\n    // Check authentication status\r\n    const isAuth = AuthService.isAuthenticated();\r\n    setIsAuthenticated(isAuth);\r\n\r\n    // Get current user and check verification status\r\n    if (isAuth) {\r\n      const user = AuthService.getCurrentUser();\r\n      setCurrentUser(user);\r\n\r\n      // Show verification notice if email is not verified\r\n      if (user && !AuthService.isEmailVerified()) {\r\n        setShowVerificationNotice(true);\r\n      } else {\r\n        setShowVerificationNotice(false);\r\n      }\r\n    } else {\r\n      setCurrentUser(null);\r\n      setShowVerificationNotice(false);\r\n    }\r\n\r\n    // Get cart item count\r\n    setCartItemCount(CartService.getItemCount());\r\n  };\r\n\r\n  // Function to refresh user data from the backend\r\n  const refreshUserData = async () => {\r\n    try {\r\n      if (AuthService.isAuthenticated()) {\r\n        console.log('Refreshing user data from backend...');\r\n        // Use the shared utility function\r\n        await refreshUserDataUtil();\r\n        updateAuthState();\r\n      }\r\n    } catch (error) {\r\n      console.error('Failed to refresh user data:', error);\r\n      // Still update from localStorage even if API call fails\r\n      updateAuthState();\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Initial update of authentication state and refresh from backend\r\n    refreshUserData();\r\n\r\n    // Add event listener for cart updates and auth changes\r\n    const handleStorageChange = () => {\r\n      // Update all authentication and cart state\r\n      updateAuthState();\r\n    };\r\n\r\n    // Set up an interval to periodically refresh user data\r\n    const refreshInterval = setInterval(() => {\r\n      if (AuthService.isAuthenticated()) {\r\n        refreshUserData();\r\n      }\r\n    }, 60000); // Refresh every minute\r\n\r\n    // Set up event listeners using the shared utility function\r\n    const cleanup = addStorageEventListeners(handleStorageChange);\r\n\r\n    return () => {\r\n      cleanup();\r\n      clearInterval(refreshInterval);\r\n    };\r\n  }, []);\r\n\r\n  const handleLogout = async () => {\r\n    await AuthService.logout();\r\n    setIsAuthenticated(false);\r\n    window.location.href = '/login';\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\">\r\n      {/* CSRF Token Component */}\r\n      <CsrfToken />\r\n\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex justify-between h-16\">\r\n            <div className=\"flex\">\r\n              <div className=\"flex-shrink-0 flex items-center\">\r\n                <Link href=\"/\" className=\"text-2xl font-bold text-indigo-600\">\r\n                  EcommerceApp\r\n                </Link>\r\n              </div>\r\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\r\n                <Link\r\n                  href=\"/\"\r\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\r\n                >\r\n                  Home\r\n                </Link>\r\n                <Link\r\n                  href=\"/products\"\r\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\r\n                >\r\n                  Products\r\n                </Link>\r\n                <Link\r\n                  href=\"/categories\"\r\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\r\n                >\r\n                  Categories\r\n                </Link>\r\n              </nav>\r\n            </div>\r\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\r\n              <Link href=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\r\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\r\n                {cartItemCount > 0 && (\r\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\r\n                    {cartItemCount}\r\n                  </span>\r\n                )}\r\n              </Link>\r\n              {isAuthenticated ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/profile\"\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\r\n                  >\r\n                    <UserIcon className=\"h-5 w-5 mr-2\" aria-hidden=\"true\" />\r\n                    My Account\r\n                  </Link>\r\n                  <button\r\n                    onClick={handleLogout}\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\r\n                  >\r\n                    Logout\r\n                  </button>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\r\n                  >\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              )}\r\n            </div>\r\n            <div className=\"-mr-2 flex items-center sm:hidden\">\r\n              <button\r\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\r\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 cursor-pointer\"\r\n              >\r\n                <span className=\"sr-only\">Open main menu</span>\r\n                {isMenuOpen ? (\r\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\r\n                ) : (\r\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Mobile menu */}\r\n        {isMenuOpen && (\r\n          <div className=\"sm:hidden\">\r\n            <div className=\"pt-2 pb-3 space-y-1\">\r\n              <Link\r\n                href=\"/\"\r\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                onClick={() => setIsMenuOpen(false)}\r\n              >\r\n                Home\r\n              </Link>\r\n              <Link\r\n                href=\"/products\"\r\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                onClick={() => setIsMenuOpen(false)}\r\n              >\r\n                Products\r\n              </Link>\r\n              <Link\r\n                href=\"/categories\"\r\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                onClick={() => setIsMenuOpen(false)}\r\n              >\r\n                Categories\r\n              </Link>\r\n              <Link\r\n                href=\"/cart\"\r\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                onClick={() => setIsMenuOpen(false)}\r\n              >\r\n                Cart ({cartItemCount})\r\n              </Link>\r\n              {isAuthenticated ? (\r\n                <>\r\n                  <Link\r\n                    href=\"/profile\"\r\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    My Account\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      handleLogout();\r\n                      setIsMenuOpen(false);\r\n                    }}\r\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 cursor-pointer\"\r\n                  >\r\n                    Logout\r\n                  </button>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    href=\"/register\"\r\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\r\n                    onClick={() => setIsMenuOpen(false)}\r\n                  >\r\n                    Register\r\n                  </Link>\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </header>\r\n\r\n      {/* Main content */}\r\n      <main className=\"flex-grow\">\r\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\r\n          {/* Verification notice */}\r\n          {showVerificationNotice && currentUser && (\r\n            <VerificationNotice\r\n              email={currentUser.email}\r\n              showDismiss={true}\r\n              onDismiss={() => setShowVerificationNotice(false)}\r\n            />\r\n          )}\r\n\r\n          {children}\r\n        </div>\r\n      </main>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-white\">\r\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\r\n          <p className=\"text-center text-gray-500 text-sm\">\r\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAce,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,4DAA4D;IAC5D,MAAM,kBAAkB;QACtB,8BAA8B;QAC9B,MAAM,SAAS,+KAAA,CAAA,cAAW,CAAC,eAAe;QAC1C,mBAAmB;QAEnB,iDAAiD;QACjD,IAAI,QAAQ;YACV,MAAM,OAAO,+KAAA,CAAA,cAAW,CAAC,cAAc;YACvC,eAAe;YAEf,oDAAoD;YACpD,IAAI,QAAQ,CAAC,+KAAA,CAAA,cAAW,CAAC,eAAe,IAAI;gBAC1C,0BAA0B;YAC5B,OAAO;gBACL,0BAA0B;YAC5B;QACF,OAAO;YACL,eAAe;YACf,0BAA0B;QAC5B;QAEA,sBAAsB;QACtB,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;IAC3C;IAEA,iDAAiD;IACjD,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,+KAAA,CAAA,cAAW,CAAC,eAAe,IAAI;gBACjC,QAAQ,GAAG,CAAC;gBACZ,kCAAkC;gBAClC,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAmB,AAAD;gBACxB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wDAAwD;YACxD;QACF;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,kEAAkE;YAClE;YAEA,uDAAuD;YACvD,MAAM;8DAAsB;oBAC1B,2CAA2C;oBAC3C;gBACF;;YAEA,uDAAuD;YACvD,MAAM,kBAAkB;0DAAY;oBAClC,IAAI,+KAAA,CAAA,cAAW,CAAC,eAAe,IAAI;wBACjC;oBACF;gBACF;yDAAG,QAAQ,uBAAuB;YAElC,2DAA2D;YAC3D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD,EAAE;YAEzC;0CAAO;oBACL;oBACA,cAAc;gBAChB;;QACF;iCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,+KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,kIAAA,CAAA,UAAS;;;;;0BAGV,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAIhE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;8DAC3B,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;;sEAEV,6LAAC,kNAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAe,eAAY;;;;;;wDAAS;;;;;;;8DAG1D,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;wBAEZ,0BAA0B,6BACzB,6LAAC,2IAAA,CAAA,UAAkB;4BACjB,OAAO,YAAY,KAAK;4BACxB,aAAa;4BACb,WAAW,IAAM,0BAA0B;;;;;;wBAI9C;;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GAnRwB;KAAA", "debugId": null}}]}