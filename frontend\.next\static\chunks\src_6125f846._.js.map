{"version": 3, "sources": [], "sections": [{"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/services/simple-api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Create a simple Axios instance without CSRF token requirements\nconst simpleApi = axios.create({\n    baseURL: '',\n    headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n    }\n});\n\n// Simple product fetching without authentication\nexport const fetchProductsSimple = async () => {\n    try {\n        console.log('Simple API: Fetching products...');\n        const response = await simpleApi.get('/api/products');\n        console.log('Simple API: Products response:', response.data);\n        return response.data;\n    } catch (error) {\n        console.error('Simple API: Error fetching products:', error);\n        throw error;\n    }\n};\n\n// Simple category fetching without authentication\nexport const fetchCategoriesSimple = async () => {\n    try {\n        console.log('Simple API: Fetching categories...');\n        const response = await simpleApi.get('/api/categories');\n        console.log('Simple API: Categories response:', response.data);\n        return response.data;\n    } catch (error) {\n        console.error('Simple API: Error fetching categories:', error);\n        throw error;\n    }\n};\n\nexport default simpleApi;\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,iEAAiE;AACjE,MAAM,YAAY,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3B,SAAS;IACT,SAAS;QACL,gBAAgB;QAChB,UAAU;IACd;AACJ;AAGO,MAAM,sBAAsB;IAC/B,IAAI;QACA,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC;QACrC,QAAQ,GAAG,CAAC,kCAAkC,SAAS,IAAI;QAC3D,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM;IACV;AACJ;AAGO,MAAM,wBAAwB;IACjC,IAAI;QACA,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,UAAU,GAAG,CAAC;QACrC,QAAQ,GAAG,CAAC,oCAAoC,SAAS,IAAI;QAC7D,OAAO,SAAS,IAAI;IACxB,EAAE,OAAO,OAAO;QACZ,QAAQ,KAAK,CAAC,0CAA0C;QACxD,MAAM;IACV;AACJ;uCAEe", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { ShoppingCartIcon, UserIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';\nimport { AuthService, CartService } from '../services';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [cartItemCount, setCartItemCount] = useState(0);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    // Check authentication status\n    setIsAuthenticated(AuthService.isAuthenticated());\n    \n    // Get cart item count\n    setCartItemCount(CartService.getItemCount());\n    \n    // Add event listener for cart updates\n    const handleStorageChange = () => {\n      setCartItemCount(CartService.getItemCount());\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    \n    return () => {\n      window.removeEventListener('storage', handleStorageChange);\n    };\n  }, []);\n\n  const handleLogout = async () => {\n    await AuthService.logout();\n    setIsAuthenticated(false);\n    navigate('/login');\n  };\n\n  return (\n    <div className=\"min-h-screen flex flex-col\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between h-16\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0 flex items-center\">\n                <Link to=\"/\" className=\"text-2xl font-bold text-indigo-600\">\n                  EcommerceApp\n                </Link>\n              </div>\n              <nav className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n                <Link\n                  to=\"/\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Home\n                </Link>\n                <Link\n                  to=\"/products\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Products\n                </Link>\n                <Link\n                  to=\"/categories\"\n                  className=\"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\"\n                >\n                  Categories\n                </Link>\n              </nav>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:items-center sm:space-x-4\">\n              <Link to=\"/cart\" className=\"relative p-1 rounded-full text-gray-400 hover:text-gray-500\">\n                <ShoppingCartIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                {cartItemCount > 0 && (\n                  <span className=\"absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-indigo-600 rounded-full\">\n                    {cartItemCount}\n                  </span>\n                )}\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"p-1 rounded-full text-gray-400 hover:text-gray-500\"\n                  >\n                    <UserIcon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                  </Link>\n                  <button\n                    onClick={handleLogout}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-gray-50\"\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700\"\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n            <div className=\"-mr-2 flex items-center sm:hidden\">\n              <button\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n              >\n                <span className=\"sr-only\">Open main menu</span>\n                {isMenuOpen ? (\n                  <XMarkIcon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                ) : (\n                  <Bars3Icon className=\"block h-6 w-6\" aria-hidden=\"true\" />\n                )}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"sm:hidden\">\n            <div className=\"pt-2 pb-3 space-y-1\">\n              <Link\n                to=\"/\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link\n                to=\"/products\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Products\n              </Link>\n              <Link\n                to=\"/categories\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link\n                to=\"/cart\"\n                className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Cart ({cartItemCount})\n              </Link>\n              {isAuthenticated ? (\n                <>\n                  <Link\n                    to=\"/profile\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Profile\n                  </Link>\n                  <button\n                    onClick={() => {\n                      handleLogout();\n                      setIsMenuOpen(false);\n                    }}\n                    className=\"block w-full text-left pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                  >\n                    Logout\n                  </button>\n                </>\n              ) : (\n                <>\n                  <Link\n                    to=\"/login\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Login\n                  </Link>\n                  <Link\n                    to=\"/register\"\n                    className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800\"\n                    onClick={() => setIsMenuOpen(false)}\n                  >\n                    Register\n                  </Link>\n                </>\n              )}\n            </div>\n          </div>\n        )}\n      </header>\n\n      {/* Main content */}\n      <main className=\"flex-grow\">\n        <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n          {children}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white\">\n        <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n          <p className=\"text-center text-gray-500 text-sm\">\n            &copy; {new Date().getFullYear()} EcommerceApp. All rights reserved.\n          </p>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;;;AALA;;;;;AAWA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,8BAA8B;YAC9B,mBAAmB,+KAAA,CAAA,cAAW,CAAC,eAAe;YAE9C,sBAAsB;YACtB,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;YAEzC,sCAAsC;YACtC,MAAM;wDAAsB;oBAC1B,iBAAiB,+KAAA,CAAA,cAAW,CAAC,YAAY;gBAC3C;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YAEnC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,WAAW;gBACxC;;QACF;2BAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,+KAAA,CAAA,cAAW,CAAC,MAAM;QACxB,mBAAmB;QACnB,SAAS;IACX;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+KAAA,CAAA,OAAI;gDAAC,IAAG;gDAAI,WAAU;0DAAqC;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAKL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+KAAA,CAAA,OAAI;4CAAC,IAAG;4CAAQ,WAAU;;8DACzB,6LAAC,kOAAA,CAAA,mBAAgB;oDAAC,WAAU;oDAAU,eAAY;;;;;;gDACjD,gBAAgB,mBACf,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;wCAIN,gCACC;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DAEV,cAAA,6LAAC,kNAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAU,eAAY;;;;;;;;;;;8DAE5C,6LAAC;oDACC,SAAS;oDACT,WAAU;8DACX;;;;;;;yEAKH;;8DACE,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;8DAGD,6LAAC,+KAAA,CAAA,OAAI;oDACH,IAAG;oDACH,WAAU;8DACX;;;;;;;;;;;;;;8CAMP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;4CACzB,2BACC,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;qEAEjD,6LAAC,oNAAA,CAAA,YAAS;gDAAC,WAAU;gDAAgB,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ1D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;8CAC9B;;;;;;8CAGD,6LAAC,+KAAA,CAAA,OAAI;oCACH,IAAG;oCACH,WAAU;oCACV,SAAS,IAAM,cAAc;;wCAC9B;wCACQ;wCAAc;;;;;;;gCAEtB,gCACC;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC;4CACC,SAAS;gDACP;gDACA,cAAc;4CAChB;4CACA,WAAU;sDACX;;;;;;;iEAKH;;sDACE,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;sDAGD,6LAAC,+KAAA,CAAA,OAAI;4CACH,IAAG;4CACH,WAAU;4CACV,SAAS,IAAM,cAAc;sDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAKL,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAoC;4BACvC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GApNM;;QAIa,+KAAA,CAAA,cAAW;;;KAJxB;uCAsNS", "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/ProductCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Product, CartService } from '../services';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nconst ProductCard: React.FC<ProductCardProps> = ({ product }) => {\n  const handleAddToCart = () => {\n    CartService.addToCart(product, 1);\n    // Trigger storage event to update cart count in Layout\n    window.dispatchEvent(new Event('storage'));\n  };\n\n  return (\n    <div className=\"group relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden\">\n      <div className=\"aspect-w-3 aspect-h-4 bg-gray-200 group-hover:opacity-75 h-48\">\n        {product.image ? (\n          <img\n            src={product.image}\n            alt={product.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"flex flex-col space-y-2 p-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">\n          <Link href={`/products/${product.slug}`} className=\"block\">\n            <span aria-hidden=\"true\" className=\"absolute inset-0\" />\n            {product.name}\n          </Link>\n        </h3>\n        <p className=\"text-sm text-gray-500 line-clamp-2\">{product.description}</p>\n        <div className=\"flex justify-between items-center\">\n          <p className=\"text-lg font-medium text-gray-900\">${Number(product.price).toFixed(2)}</p>\n          {product.quantity > 0 ? (\n            <span className=\"text-sm text-green-600\">In Stock</span>\n          ) : (\n            <span className=\"text-sm text-red-600\">Out of Stock</span>\n          )}\n        </div>\n        <button\n          onClick={handleAddToCart}\n          disabled={product.quantity <= 0}\n          className={`mt-2 w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white ${\n            product.quantity > 0\n              ? 'bg-indigo-600 hover:bg-indigo-700'\n              : 'bg-gray-400 cursor-not-allowed'\n          }`}\n        >\n          Add to Cart\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,OAAO,EAAE;IAC1D,MAAM,kBAAkB;QACtB,+KAAA,CAAA,cAAW,CAAC,SAAS,CAAC,SAAS;QAC/B,uDAAuD;QACvD,OAAO,aAAa,CAAC,IAAI,MAAM;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,KAAK,iBACZ,6LAAC;oBACC,KAAK,QAAQ,KAAK;oBAClB,KAAK,QAAQ,IAAI;oBACjB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,IAAI,EAAE;4BAAE,WAAU;;8CACjD,6LAAC;oCAAK,eAAY;oCAAO,WAAU;;;;;;gCAClC,QAAQ,IAAI;;;;;;;;;;;;kCAGjB,6LAAC;wBAAE,WAAU;kCAAsC,QAAQ,WAAW;;;;;;kCACtE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAAoC;oCAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,CAAC;;;;;;;4BAChF,QAAQ,QAAQ,GAAG,kBAClB,6LAAC;gCAAK,WAAU;0CAAyB;;;;;qDAEzC,6LAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;kCAG3C,6LAAC;wBACC,SAAS;wBACT,UAAU,QAAQ,QAAQ,IAAI;wBAC9B,WAAW,CAAC,kIAAkI,EAC5I,QAAQ,QAAQ,GAAG,IACf,sCACA,kCACJ;kCACH;;;;;;;;;;;;;;;;;;AAMT;KApDM;uCAsDS", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Category } from '../services';\n\ninterface CategoryCardProps {\n  category: Category;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category }) => {\n  return (\n    <Link\n      href={`/categories/${category.slug}`}\n      className=\"group block bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\"\n    >\n      <div className=\"aspect-w-3 aspect-h-2 bg-gray-200 group-hover:opacity-75 h-40\">\n        {category.image ? (\n          <img\n            src={category.image}\n            alt={category.name}\n            className=\"w-full h-full object-center object-cover\"\n          />\n        ) : (\n          <div className=\"w-full h-full flex items-center justify-center bg-gray-100\">\n            <span className=\"text-gray-400\">No image</span>\n          </div>\n        )}\n      </div>\n      <div className=\"p-4\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{category.name}</h3>\n        {category.description && (\n          <p className=\"mt-1 text-sm text-gray-500 line-clamp-2\">{category.description}</p>\n        )}\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;;;AAOA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IAC7D,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM,CAAC,YAAY,EAAE,SAAS,IAAI,EAAE;QACpC,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,iBACb,6LAAC;oBACC,KAAK,SAAS,KAAK;oBACnB,KAAK,SAAS,IAAI;oBAClB,WAAU;;;;;yCAGZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAItC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqC,SAAS,IAAI;;;;;;oBAC/D,SAAS,WAAW,kBACnB,6LAAC;wBAAE,WAAU;kCAA2C,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAKtF;KA3BM;uCA6BS", "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/StripePaymentForm.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useStripe, useElements, CardElement } from \"@stripe/react-stripe-js\";\nimport { StripeCardElementOptions } from \"@stripe/stripe-js\";\n\ninterface StripePaymentFormProps {\n  onSuccess: (paymentMethodId: string) => void;\n  onError: (error: string) => void;\n  loading: boolean;\n  setLoading: (loading: boolean) => void;\n}\n\nconst cardElementOptions: StripeCardElementOptions = {\n  style: {\n    base: {\n      fontSize: \"16px\",\n      color: \"#424770\",\n      \"::placeholder\": {\n        color: \"#aab7c4\",\n      },\n    },\n    invalid: {\n      color: \"#9e2146\",\n    },\n  },\n};\n\nconst StripePaymentForm: React.FC<StripePaymentFormProps> = ({\n  onSuccess,\n  onError,\n  loading,\n  setLoading,\n}) => {\n  const stripe = useStripe();\n  const elements = useElements();\n  const [cardError, setCardError] = useState<string | null>(null);\n\n  const handleSubmit = async (event: React.FormEvent) => {\n    event.preventDefault();\n\n    if (!stripe || !elements) {\n      return;\n    }\n\n    const cardElement = elements.getElement(CardElement);\n\n    if (!cardElement) {\n      return;\n    }\n\n    setLoading(true);\n    setCardError(null);\n\n    try {\n      // Create payment method\n      const { error, paymentMethod } = await stripe.createPaymentMethod({\n        type: \"card\",\n        card: cardElement,\n      });\n\n      if (error) {\n        setCardError(error.message || \"An error occurred during payment\");\n        onError(error.message || \"An error occurred during payment\");\n      } else if (paymentMethod) {\n        onSuccess(paymentMethod.id);\n      }\n    } catch {\n      setCardError(\"Payment failed. Please try again.\");\n      onError(\"Payment failed. Please try again.\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCardChange = (event: { error?: { message: string } }) => {\n    if (event.error) {\n      setCardError(event.error.message);\n    } else {\n      setCardError(null);\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Card Information\n        </label>\n        <div className=\"border border-gray-300 rounded-md p-3 bg-white\">\n          <CardElement\n            options={cardElementOptions}\n            onChange={handleCardChange}\n          />\n        </div>\n        {cardError && <p className=\"mt-1 text-sm text-red-600\">{cardError}</p>}\n      </div>\n\n      <div className=\"bg-blue-50 border-l-4 border-blue-400 p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-blue-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-blue-700\">\n              This is a test environment. Use test card number 4242 4242 4242\n              4242 with any future expiry date and any 3-digit CVC.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <button\n        type=\"submit\"\n        disabled={!stripe || loading}\n        className={`w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white ${\n          !stripe || loading\n            ? \"bg-gray-400 cursor-not-allowed\"\n            : \"bg-indigo-600 hover:bg-indigo-700 cursor-pointer\"\n        }`}\n      >\n        {loading ? (\n          <>\n            <div className=\"animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2\"></div>\n            Processing Payment...\n          </>\n        ) : (\n          \"Pay Now\"\n        )}\n      </button>\n    </form>\n  );\n};\n\nexport default StripePaymentForm;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAUA,MAAM,qBAA+C;IACnD,OAAO;QACL,MAAM;YACJ,UAAU;YACV,OAAO;YACP,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,SAAS;YACP,OAAO;QACT;IACF;AACF;AAEA,MAAM,oBAAsD,CAAC,EAC3D,SAAS,EACT,OAAO,EACP,OAAO,EACP,UAAU,EACX;;IACC,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,eAAe,OAAO;QAC1B,MAAM,cAAc;QAEpB,IAAI,CAAC,UAAU,CAAC,UAAU;YACxB;QACF;QAEA,MAAM,cAAc,SAAS,UAAU,CAAC,sLAAA,CAAA,cAAW;QAEnD,IAAI,CAAC,aAAa;YAChB;QACF;QAEA,WAAW;QACX,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,OAAO,mBAAmB,CAAC;gBAChE,MAAM;gBACN,MAAM;YACR;YAEA,IAAI,OAAO;gBACT,aAAa,MAAM,OAAO,IAAI;gBAC9B,QAAQ,MAAM,OAAO,IAAI;YAC3B,OAAO,IAAI,eAAe;gBACxB,UAAU,cAAc,EAAE;YAC5B;QACF,EAAE,OAAM;YACN,aAAa;YACb,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,MAAM,KAAK,EAAE;YACf,aAAa,MAAM,KAAK,CAAC,OAAO;QAClC,OAAO;YACL,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAK,UAAU;QAAc,WAAU;;0BACtC,6LAAC;;kCACC,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,sLAAA,CAAA,cAAW;4BACV,SAAS;4BACT,UAAU;;;;;;;;;;;oBAGb,2BAAa,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG1D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAQ;gCACR,MAAK;0CAEL,cAAA,6LAAC;oCACC,UAAS;oCACT,GAAE;oCACF,UAAS;;;;;;;;;;;;;;;;sCAIf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBACC,MAAK;gBACL,UAAU,CAAC,UAAU;gBACrB,WAAW,CAAC,+HAA+H,EACzI,CAAC,UAAU,UACP,mCACA,oDACJ;0BAED,wBACC;;sCACE,6LAAC;4BAAI,WAAU;;;;;;wBAAkF;;mCAInG;;;;;;;;;;;;AAKV;GAlHM;;QAMW,sLAAA,CAAA,YAAS;QACP,sLAAA,CAAA,cAAW;;;KAPxB;uCAoHS", "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/components/index.ts"], "sourcesContent": ["export { default as Layout } from './Layout';\nexport { default as ProductCard } from './ProductCard';\nexport { default as CategoryCard } from './CategoryCard';\nexport { default as VerificationNotice } from './VerificationNotice';\nexport { default as CsrfToken } from './CsrfToken';\nexport { default as StripePaymentForm } from './StripePaymentForm';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/HomePage.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport {\n  ProductService,\n  CategoryService,\n  Product,\n  Category,\n} from \"../services\";\nimport { initializeApi } from \"../services/api\";\nimport {\n  fetchProductsSimple,\n  fetchCategoriesSimple,\n} from \"../services/simple-api\";\nimport { ProductCard, CategoryCard } from \"../components\";\n\nconst HomePage: React.FC = () => {\n  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        console.log(\"HomePage useEffect - Starting data fetch...\");\n        setLoading(true);\n\n        // Initialize API first\n        console.log(\"HomePage useEffect - Initializing API...\");\n        await initializeApi();\n        console.log(\"HomePage useEffect - API initialized\");\n\n        console.log(\"HomePage useEffect - Fetching data...\");\n        const [productsData, categoriesData] = await Promise.all([\n          ProductService.getFeaturedProducts(),\n          CategoryService.getAllCategories(),\n        ]);\n\n        // Safely access the data arrays\n        console.log(\"HomePage - productsData:\", productsData);\n        console.log(\"HomePage - categoriesData:\", categoriesData);\n\n        if (productsData && productsData.data) {\n          console.log(\n            \"HomePage - setting featured products:\",\n            productsData.data\n          );\n          setFeaturedProducts(\n            Array.isArray(productsData.data) ? productsData.data : []\n          );\n        } else {\n          console.log(\"HomePage - No products data found\");\n        }\n\n        if (categoriesData && categoriesData.data) {\n          console.log(\"HomePage - setting categories:\", categoriesData.data);\n          setCategories(\n            Array.isArray(categoriesData.data) ? categoriesData.data : []\n          );\n        } else {\n          console.log(\"HomePage - No categories data found\");\n        }\n\n        setError(null);\n        console.log(\"HomePage useEffect - Data fetch completed successfully\");\n      } catch (err: any) {\n        console.error(\"Error fetching data:\", err);\n        console.error(\"Error details:\", err.response?.data || err.message);\n        setError(\"Failed to load data. Please try again later.\");\n        setFeaturedProducts([]);\n        setCategories([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border-l-4 border-red-400 p-4 my-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <svg\n              className=\"h-5 w-5 text-red-400\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm text-red-700\">{error}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      {/* Test button for debugging */}\n      <div className=\"mb-4\">\n        <button\n          onClick={testApiCall}\n          className=\"bg-red-500 text-white px-4 py-2 rounded mr-2\"\n        >\n          Test API Call\n        </button>\n        <button\n          onClick={async () => {\n            try {\n              console.log(\"Direct API test without CSRF...\");\n              const response = await fetch(\"/api/products\");\n              const data = await response.json();\n              console.log(\"Direct fetch result:\", data);\n            } catch (error: any) {\n              console.error(\"Direct fetch error:\", error);\n            }\n          }}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded mr-2\"\n        >\n          Test Direct Fetch\n        </button>\n        <button\n          onClick={async () => {\n            try {\n              console.log(\"Simple API test...\");\n              const [productsData, categoriesData] = await Promise.all([\n                fetchProductsSimple(),\n                fetchCategoriesSimple(),\n              ]);\n              console.log(\"Simple API products:\", productsData);\n              console.log(\"Simple API categories:\", categoriesData);\n\n              // Update state with simple API data\n              if (productsData && productsData.data) {\n                setFeaturedProducts(\n                  Array.isArray(productsData.data)\n                    ? productsData.data.filter((p) => p.featured)\n                    : []\n                );\n              }\n              if (categoriesData && categoriesData.data) {\n                setCategories(\n                  Array.isArray(categoriesData.data) ? categoriesData.data : []\n                );\n              }\n            } catch (error: any) {\n              console.error(\"Simple API error:\", error);\n            }\n          }}\n          className=\"bg-green-500 text-white px-4 py-2 rounded\"\n        >\n          Test Simple API\n        </button>\n        <p className=\"text-sm text-gray-600 mt-2\">\n          Featured Products: {featuredProducts.length} | Categories:{\" \"}\n          {categories.length} | Loading: {loading.toString()} | Error:{\" \"}\n          {error || \"none\"}\n        </p>\n        <div className=\"text-xs text-gray-500 mt-1\">\n          <p>\n            Featured Products Array:{\" \"}\n            {JSON.stringify(featuredProducts.slice(0, 1))}\n          </p>\n          <p>Categories Array: {JSON.stringify(categories.slice(0, 1))}</p>\n        </div>\n      </div>\n\n      {/* Hero section */}\n      <div className=\"bg-indigo-700 rounded-lg shadow-xl overflow-hidden\">\n        <div className=\"px-4 py-12 sm:px-6 lg:px-8 lg:py-16\">\n          <div className=\"max-w-2xl mx-auto text-center\">\n            <h1 className=\"text-3xl font-extrabold text-white sm:text-4xl\">\n              <span className=\"block\">Welcome to EcommerceApp</span>\n            </h1>\n            <p className=\"mt-4 text-lg leading-6 text-indigo-100\">\n              Discover amazing products at great prices. Shop now and enjoy fast\n              delivery!\n            </p>\n            <div className=\"mt-8 flex justify-center\">\n              <div className=\"inline-flex rounded-md shadow\">\n                <Link\n                  href=\"/products\"\n                  className=\"inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50\"\n                >\n                  Browse Products\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured products section */}\n      <div className=\"mt-12\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">\n            Featured Products\n          </h2>\n          <Link\n            href=\"/products\"\n            className=\"text-indigo-600 hover:text-indigo-500\"\n          >\n            View all\n          </Link>\n        </div>\n        {featuredProducts.length > 0 ? (\n          <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-4\">\n            {featuredProducts.map((product) => (\n              <ProductCard key={product.id} product={product} />\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-gray-500\">No featured products available.</p>\n        )}\n      </div>\n\n      {/* Categories section */}\n      <div className=\"mt-12\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900\">Shop by Category</h2>\n          <Link\n            href=\"/categories\"\n            className=\"text-indigo-600 hover:text-indigo-500\"\n          >\n            View all\n          </Link>\n        </div>\n        {categories.length > 0 ? (\n          <div className=\"grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3\">\n            {categories.slice(0, 3).map((category) => (\n              <CategoryCard key={category.id} category={category} />\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-gray-500\">No categories available.</p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAMA;AACA;AAIA;AAAA;AAAA;;;AAfA;;;;;;;AAiBA,MAAM,WAAqB;;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;gDAAY;oBAChB,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,WAAW;wBAEX,uBAAuB;wBACvB,QAAQ,GAAG,CAAC;wBACZ,MAAM,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;wBAClB,QAAQ,GAAG,CAAC;wBAEZ,QAAQ,GAAG,CAAC;wBACZ,MAAM,CAAC,cAAc,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACvD,qLAAA,CAAA,iBAAc,CAAC,mBAAmB;4BAClC,uLAAA,CAAA,kBAAe,CAAC,gBAAgB;yBACjC;wBAED,gCAAgC;wBAChC,QAAQ,GAAG,CAAC,4BAA4B;wBACxC,QAAQ,GAAG,CAAC,8BAA8B;wBAE1C,IAAI,gBAAgB,aAAa,IAAI,EAAE;4BACrC,QAAQ,GAAG,CACT,yCACA,aAAa,IAAI;4BAEnB,oBACE,MAAM,OAAO,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,GAAG,EAAE;wBAE7D,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACd;wBAEA,IAAI,kBAAkB,eAAe,IAAI,EAAE;4BACzC,QAAQ,GAAG,CAAC,kCAAkC,eAAe,IAAI;4BACjE,cACE,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;wBAEjE,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACd;wBAEA,SAAS;wBACT,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,QAAQ,KAAK,CAAC,kBAAkB,IAAI,QAAQ,EAAE,QAAQ,IAAI,OAAO;wBACjE,SAAS;wBACT,oBAAoB,EAAE;wBACtB,cAAc,EAAE;oBAClB,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,6LAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;kCAIf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,qBACE,6LAAC;;0BAEC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;4BACP,IAAI;gCACF,QAAQ,GAAG,CAAC;gCACZ,MAAM,WAAW,MAAM,MAAM;gCAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gCAChC,QAAQ,GAAG,CAAC,wBAAwB;4BACtC,EAAE,OAAO,OAAY;gCACnB,QAAQ,KAAK,CAAC,uBAAuB;4BACvC;wBACF;wBACA,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBACC,SAAS;4BACP,IAAI;gCACF,QAAQ,GAAG,CAAC;gCACZ,MAAM,CAAC,cAAc,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;oCACvD,CAAA,GAAA,mIAAA,CAAA,sBAAmB,AAAD;oCAClB,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD;iCACrB;gCACD,QAAQ,GAAG,CAAC,wBAAwB;gCACpC,QAAQ,GAAG,CAAC,0BAA0B;gCAEtC,oCAAoC;gCACpC,IAAI,gBAAgB,aAAa,IAAI,EAAE;oCACrC,oBACE,MAAM,OAAO,CAAC,aAAa,IAAI,IAC3B,aAAa,IAAI,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,QAAQ,IAC1C,EAAE;gCAEV;gCACA,IAAI,kBAAkB,eAAe,IAAI,EAAE;oCACzC,cACE,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;gCAEjE;4BACF,EAAE,OAAO,OAAY;gCACnB,QAAQ,KAAK,CAAC,qBAAqB;4BACrC;wBACF;wBACA,WAAU;kCACX;;;;;;kCAGD,6LAAC;wBAAE,WAAU;;4BAA6B;4BACpB,iBAAiB,MAAM;4BAAC;4BAAe;4BAC1D,WAAW,MAAM;4BAAC;4BAAa,QAAQ,QAAQ;4BAAG;4BAAU;4BAC5D,SAAS;;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCACwB;oCACxB,KAAK,SAAS,CAAC,iBAAiB,KAAK,CAAC,GAAG;;;;;;;0CAE5C,6LAAC;;oCAAE;oCAAmB,KAAK,SAAS,CAAC,WAAW,KAAK,CAAC,GAAG;;;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAK,WAAU;8CAAQ;;;;;;;;;;;0CAE1B,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAItD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;oBAIF,iBAAiB,MAAM,GAAG,kBACzB,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,8KAAA,CAAA,cAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;6CAIhC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;oBAIF,WAAW,MAAM,GAAG,kBACnB,6LAAC;wBAAI,WAAU;kCACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC3B,6LAAC,gLAAA,CAAA,eAAY;gCAAmB,UAAU;+BAAvB,SAAS,EAAE;;;;;;;;;6CAIlC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC;GAhPM;KAAA;uCAkPS", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/LoginPage.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { AuthService } from '../services';\n\nconst LoginPage: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    try {\n      setLoading(true);\n      setError(null);\n      \n      await AuthService.login({ email, password });\n      navigate('/');\n    } catch (err: any) {\n      console.error('Login error:', err);\n      setError(err.response?.data?.message || 'Invalid email or password');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Sign in to your account</h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link to=\"/register\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n            create a new account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          {error && (\n            <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm text-red-700\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${\n                  loading ? 'opacity-70 cursor-not-allowed' : ''\n                }`}\n              >\n                {loading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,+K<PERSON><PERSON>,CAAA,cAAW,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAC1C,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;QAC1C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,6LAAC,+KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAY,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMvF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,WAAU;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;8CACC,cAAA,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,+NAA+N,EACzO,UAAU,kCAAkC,IAC5C;kDAED,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C;GA1GM;;QAKa,+KAAA,CAAA,cAAW;;;KALxB;uCA4GS", "debugId": null}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/page-components/RegisterPage.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { AuthService } from '../services';\n\nconst RegisterPage: React.FC = () => {\n  const [name, setName] = useState('');\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [passwordConfirmation, setPasswordConfirmation] = useState('');\n  const [error, setError] = useState<string | null>(null);\n  const [errors, setErrors] = useState<Record<string, string[]>>({});\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (password !== passwordConfirmation) {\n      setError('Passwords do not match');\n      return;\n    }\n    \n    try {\n      setLoading(true);\n      setError(null);\n      setErrors({});\n      \n      await AuthService.register({\n        name,\n        email,\n        password,\n        password_confirmation: passwordConfirmation,\n      });\n      \n      navigate('/');\n    } catch (err: any) {\n      console.error('Registration error:', err);\n      \n      if (err.response?.data?.errors) {\n        setErrors(err.response.data.errors);\n      } else {\n        setError(err.response?.data?.message || 'Registration failed. Please try again.');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-full flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">Create a new account</h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link to=\"/login\" className=\"font-medium text-indigo-600 hover:text-indigo-500\">\n            sign in to your existing account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\">\n          {error && (\n            <div className=\"bg-red-50 border-l-4 border-red-400 p-4 mb-6\">\n              <div className=\"flex\">\n                <div className=\"flex-shrink-0\">\n                  <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <div className=\"ml-3\">\n                  <p className=\"text-sm text-red-700\">{error}</p>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                Name\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  autoComplete=\"name\"\n                  required\n                  value={name}\n                  onChange={(e) => setName(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.name && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.name[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.email ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.email && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.email[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className={`appearance-none block w-full px-3 py-2 border ${\n                    errors.password ? 'border-red-300' : 'border-gray-300'\n                  } rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}\n                />\n                {errors.password && (\n                  <p className=\"mt-2 text-sm text-red-600\">{errors.password[0]}</p>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password_confirmation\" className=\"block text-sm font-medium text-gray-700\">\n                Confirm Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password_confirmation\"\n                  name=\"password_confirmation\"\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  required\n                  value={passwordConfirmation}\n                  onChange={(e) => setPasswordConfirmation(e.target.value)}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${\n                  loading ? 'opacity-70 cursor-not-allowed' : ''\n                }`}\n              >\n                {loading ? 'Creating account...' : 'Create account'}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,eAAyB;;IAC7B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,aAAa,sBAAsB;YACrC,SAAS;YACT;QACF;QAEA,IAAI;YACF,WAAW;YACX,SAAS;YACT,UAAU,CAAC;YAEX,MAAM,+KAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBACzB;gBACA;gBACA;gBACA,uBAAuB;YACzB;YAEA,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YAErC,IAAI,IAAI,QAAQ,EAAE,MAAM,QAAQ;gBAC9B,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM;YACpC,OAAO;gBACL,SAAS,IAAI,QAAQ,EAAE,MAAM,WAAW;YAC1C;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,6LAAC,+KAAA,CAAA,OAAI;gCAAC,IAAG;gCAAS,WAAU;0CAAoD;;;;;;;;;;;;;;;;;;0BAMpF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAuB,SAAQ;4CAAY,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,UAAS;gDAAU,GAAE;gDAA0N,UAAS;;;;;;;;;;;;;;;;kDAGlQ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC;4BAAK,WAAU;4BAAY,UAAU;;8CACpC,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,WAAW,CAAC,8CAA8C,EACxD,OAAO,IAAI,GAAG,mBAAmB,kBAClC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,IAAI,kBACV,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAK9D,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAW,CAAC,8CAA8C,EACxD,OAAO,KAAK,GAAG,mBAAmB,kBACnC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,KAAK,kBACX,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAK/D,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAW,CAAC,8CAA8C,EACxD,OAAO,QAAQ,GAAG,mBAAmB,kBACtC,sHAAsH,CAAC;;;;;;gDAEzH,OAAO,QAAQ,kBACd,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;8CAKlE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAwB,WAAU;sDAA0C;;;;;;sDAG3F,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,cAAa;gDACb,QAAQ;gDACR,OAAO;gDACP,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;gDACvD,WAAU;;;;;;;;;;;;;;;;;8CAKhB,6LAAC;8CACC,cAAA,6LAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,+NAA+N,EACzO,UAAU,kCAAkC,IAC5C;kDAED,UAAU,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GAjLM;;QAQa,+KAAA,CAAA,cAAW;;;KARxB;uCAmLS", "debugId": null}}]}