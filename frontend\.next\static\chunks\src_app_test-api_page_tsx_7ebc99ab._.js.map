{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/test-api/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from 'react';\nimport axios from 'axios';\n\nexport default function TestApiPage() {\n  const [result, setResult] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const testDirectAxios = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Making direct axios request...\");\n      const response = await axios.get(\"/api/products?featured=true\", {\n        headers: {\n          Accept: \"application/json\",\n          \"Content-Type\": \"application/json\",\n        },\n      });\n      console.log(\"Axios response:\", response);\n      console.log(\"Axios response data:\", response.data);\n      setResult(response.data);\n    } catch (err: any) {\n      console.error(\"Axios test error:\", err);\n      setError(err instanceof Error ? err.message : \"Unknown error\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testSanctumCSRF = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Testing Sanctum CSRF...\");\n      const response = await axios.get(\"/sanctum/csrf-cookie\", {\n        withCredentials: true,\n        headers: {\n          Accept: \"application/json\",\n          \"Content-Type\": \"application/json\",\n        },\n      });\n      console.log(\"Sanctum CSRF response:\", response);\n      setResult({ message: \"CSRF token fetched successfully\", status: response.status });\n    } catch (err: any) {\n      console.error(\"Sanctum CSRF test error:\", err);\n      setError(err instanceof Error ? err.message : \"Unknown error\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testCategories = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Testing categories...\");\n      const response = await axios.get(\"/api/categories\", {\n        headers: {\n          Accept: \"application/json\",\n          \"Content-Type\": \"application/json\",\n        },\n      });\n      console.log(\"Categories response:\", response);\n      console.log(\"Categories response data:\", response.data);\n      setResult(response.data);\n    } catch (err: any) {\n      console.error(\"Categories test error:\", err);\n      setError(err instanceof Error ? err.message : \"Unknown error\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"container mx-auto p-8\">\n      <h1 className=\"text-3xl font-bold mb-8\">API Test Page</h1>\n      \n      <div className=\"space-y-4 mb-8\">\n        <button\n          onClick={testDirectAxios}\n          disabled={loading}\n          className=\"bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-4\"\n        >\n          {loading ? \"Loading...\" : \"Test Products API\"}\n        </button>\n        \n        <button\n          onClick={testSanctumCSRF}\n          disabled={loading}\n          className=\"bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-4\"\n        >\n          {loading ? \"Loading...\" : \"Test Sanctum CSRF\"}\n        </button>\n        \n        <button\n          onClick={testCategories}\n          disabled={loading}\n          className=\"bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded\"\n        >\n          {loading ? \"Loading...\" : \"Test Categories API\"}\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n          <strong>Error:</strong> {error}\n        </div>\n      )}\n\n      {result && (\n        <div className=\"bg-gray-100 p-4 rounded\">\n          <h2 className=\"text-xl font-bold mb-2\">Result:</h2>\n          <pre className=\"whitespace-pre-wrap overflow-auto max-h-96\">\n            {JSON.stringify(result, null, 2)}\n          </pre>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,+BAA+B;gBAC9D,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;YACF;YACA,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,QAAQ,GAAG,CAAC,wBAAwB,SAAS,IAAI;YACjD,UAAU,SAAS,IAAI;QACzB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,wBAAwB;gBACvD,iBAAiB;gBACjB,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;YACF;YACA,QAAQ,GAAG,CAAC,0BAA0B;YACtC,UAAU;gBAAE,SAAS;gBAAmC,QAAQ,SAAS,MAAM;YAAC;QAClF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,SAAS;QACT,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,mBAAmB;gBAClD,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;YACF;YACA,QAAQ,GAAG,CAAC,wBAAwB;YACpC,QAAQ,GAAG,CAAC,6BAA6B,SAAS,IAAI;YACtD,UAAU,SAAS,IAAI;QACzB,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,eAAe;;;;;;kCAG5B,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,eAAe;;;;;;kCAG5B,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,eAAe;;;;;;;;;;;;YAI7B,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAO;;;;;;oBAAe;oBAAE;;;;;;;YAI5B,wBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,6LAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;AAM1C;GArHwB;KAAA", "debugId": null}}]}